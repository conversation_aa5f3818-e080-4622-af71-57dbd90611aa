import cv2
import numpy as np
from skimage.feature import graycomatrix, graycoprops
from skimage.measure import shannon_entropy

def calculate_features(image):
    # Convert image to grayscale if it's not already
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Edge detection using Canny
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.mean(edges) / 255.0

    # Calculate GLCM (Gray-Level Co-occurrence Matrix)
    glcm = graycomatrix(gray, distances=[1], angles=[0], levels=256,
                         symmetric=True, normed=True)
    contrast = graycoprops(glcm, 'contrast')[0, 0]
    energy = graycoprops(glcm, 'energy')[0, 0]

    # Shannon entropy
    ent = shannon_entropy(gray.ravel())

    # Mean and standard deviation of pixel intensities
    mean_intensity = np.mean(gray)
    std_intensity = np.std(gray)

    return {
        'edge_density': edge_density,
        'contrast': contrast,
        'energy': energy,
        'entropy': ent,
        'mean_intensity': mean_intensity,
        'std_intensity': std_intensity
    }

def calculate_weights(features_visible, features_infrared):
    # Simple weighting based on feature values
    weights_visible = {}
    weights_infrared = {}

    for feature in features_visible:
        visible_value = features_visible[feature]
        infrared_value = features_infrared[feature]

        total_value = visible_value + infrared_value
        if total_value == 0:
            weights_visible[feature] = 0.5
            weights_infrared[feature] = 0.5
        else:
            weights_visible[feature] = visible_value / total_value
            weights_infrared[feature] = infrared_value / total_value

    return weights_visible, weights_infrared

def process_images(visible_path, infrared_path):
    visible_image = cv2.imread(visible_path)
    infrared_image = cv2.imread(infrared_path)

    if visible_image is None or infrared_image is None:
        raise ValueError("Images could not be read")

    features_visible = calculate_features(visible_image)
    features_infrared = calculate_features(infrared_image)

    weights_visible, weights_infrared = calculate_weights(features_visible, features_infrared)

    print("Visible Image Features:", features_visible)
    print("Infrared Image Features:", features_infrared)
    print("Visible Weights:", weights_visible)
    print("Infrared Weights:", weights_infrared)

if __name__ == "__main__":
    visible_image_path = "visible/000000.jpg"
    infrared_image_path = "infrared/000000.jpg"

    process_images(visible_image_path, infrared_image_path)



