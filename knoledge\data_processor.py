import numpy as np
import cv2
from torchvision import transforms
import torch
import torch.nn as nn
from skimage import exposure
class DataProcessor:
    def __init__(self, config):
        self.config = config
        self.device = config['device']
        self.scene_classifier = self._init_scene_classifier()
        
        # 初始化图像预处理转换
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Resize((224, 224)),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def _init_scene_classifier(self):
        """初始化场景分类器"""
        # 实际应用中应加载预训练模型
        class SceneClassifier(nn.Module):
            def __init__(self, num_classes=4):
                super(SceneClassifier, self).__init__()
                self.model = torch.hub.load('pytorch/vision:v0.10.0', 'resnet18', pretrained=True)
                self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)
            
            def forward(self, x):
                return self.model(x)
        
        model = SceneClassifier(len(self.config['scene_types'])).to(self.device)
        return model
    
    def preprocess(self, multi_source_data):
        """预处理多源数据"""
        processed_data = {}
        
        for sensor_type, data in multi_source_data.items():
            if sensor_type == 'visible':
                # 可见光图像预处理
                if isinstance(data, np.ndarray):
                    # 从numpy数组转换为PyTorch张量
                    data = self.transform(data).unsqueeze(0).to(self.device)
                processed_data[sensor_type] = data
                
            elif sensor_type == 'infrared':
                # 红外图像预处理
                if isinstance(data, np.ndarray):
                    # 归一化
                    data = (data - data.min()) / (data.max() - data.min() + 1e-6)
                    # 转换为RGB（复制通道）
                    data = np.repeat(data[..., np.newaxis], 3, axis=2)
                    data = self.transform(data).unsqueeze(0).to(self.device)
                processed_data[sensor_type] = data
                
            elif sensor_type == 'sar':
                # SAR图像预处理
                if isinstance(data, np.ndarray):
                    # 应用自适应直方图均衡化
                    data = exposure.equalize_adapthist(data, clip_limit=0.03)
                    # 转换为RGB
                    data = np.repeat(data[..., np.newaxis], 3, axis=2)
                    data = self.transform(data).unsqueeze(0).to(self.device)
                processed_data[sensor_type] = data
        
        return processed_data
    
    def analyze_scene(self, processed_data, environmental_data):
        """分析场景类型和特征敏感性"""
        # 1. 场景类型预测
        visible_data = processed_data['visible']
        with torch.no_grad():
            scene_logits = self.scene_classifier(visible_data)
            scene_probs = torch.softmax(scene_logits, dim=1)
            scene_idx = torch.argmax(scene_probs, dim=1).item()
            scene_type = self.config['scene_types'][scene_idx]
        
        # 2. 特征敏感性评估（基于环境数据）
        feature_sensitivity = self._assess_feature_sensitivity(scene_type, environmental_data)
        
        return scene_type, feature_sensitivity
    
    def _assess_feature_sensitivity(self, scene_type, env_data):
        """评估各特征在当前场景下的敏感性"""
        # 基于场景类型和环境数据确定各传感器的敏感性
        sensitivity_map = {
            'sunny': {'visible': 0.8, 'infrared': 0.6, 'sar': 0.4},
            'rainy': {'visible': 0.4, 'infrared': 0.7, 'sar': 0.9},
            'night': {'visible': 0.2, 'infrared': 0.9, 'sar': 0.7},
            'foggy': {'visible': 0.3, 'infrared': 0.6, 'sar': 0.8}
        }
        
        # 获取基础敏感性
        base_sensitivity = sensitivity_map.get(scene_type, {'visible': 0.6, 'infrared': 0.6, 'sar': 0.6})
        
        # 考虑干扰因素调整
        if env_data.get('interference', None) == 'electronic':
            base_sensitivity['sar'] *= 0.7
        
        if env_data.get('weather', None) == 'heavy_rain':
            base_sensitivity['visible'] *= 0.5
        
        # 归一化
        total = sum(base_sensitivity.values())
        return {k: v / total for k, v in base_sensitivity.items()}    