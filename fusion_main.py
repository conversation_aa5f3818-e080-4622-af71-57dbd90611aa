import torch
from image_fusion_model import MultiScaleFusionNet, train_model, fuse_images
from data_loader import create_data_loader

def main(train=True):
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 初始化模型
    model = MultiScaleFusionNet(num_channels=64)
    
    # 训练模型
    #train = True  # 设置为False可跳过训练，直接使用预训练模型
    if train:
        # 创建数据加载器
        visible_dir = "visible"  # 替换为实际路径
        infrared_dir = "infrared"  # 替换为实际路径
        
        train_loader = create_data_loader(visible_dir, infrared_dir, 
                                         patch_size=256, batch_size=16)
        
        # 训练模型
        trainer = train_model(train_loader, model, device, 
                             epochs=50, save_interval=10)
    else:
        # 加载预训练模型
        model_path = "models/final_model.pth"  # 替换为实际路径
        model.load_state_dict(torch.load(model_path, map_location=device)['model_state_dict'])
        model = model.to(device)
    
    # 测试模型 - 融合单对图像
    test_visible = "visible/000000.jpg"  # 替换为实际路径
    test_infrared = "infrared/000000.jpg"  # 替换为实际路径
    output_path = "results/fused_image.jpg"
    
    fused_image = fuse_images(test_visible, test_infrared, model, device, output_path)
    print("Image fusion completed!")

if __name__ == "__main__":
    main(train=False)  # 设置为True进行训练，False直接使用预训练模型进行测试 