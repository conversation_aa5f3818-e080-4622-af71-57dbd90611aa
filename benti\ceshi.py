import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt


def read_graph(file_path):
    """
    从CSV文件读取知识图谱数据

    参数:
    file_path (str): CSV文件路径

    返回:
    networkx.DiGraph: 表示知识图谱的有向图
    """
    # 读取CSV文件
    df = pd.read_csv(file_path)

    # 创建有向图
    G = nx.DiGraph()

    # 添加节点和边
    for _, row in df.iterrows():
        head_entity = row['Head Entity']
        head_attribute = row['Head Attribute']
        relation = row['Relation']
        tail_entity = row['Tail Entity']
        tail_attribute = row['Tail Attribute']

        # 添加头实体节点，节点属性包含实体属性
        G.add_node(head_entity, attribute=head_attribute)
        # 添加尾实体节点，节点属性包含实体属性
        G.add_node(tail_entity, attribute=tail_attribute)
        # 添加边，边属性包含关系类型
        G.add_edge(head_entity, tail_entity, relation=relation)

    return G


def export_node(G, head_entity=None, relation=None, tail_entity=None):
    """
    根据指定条件筛选子知识图谱

    参数:
    G (networkx.DiGraph): 原始知识图谱
    head_entity (str, optional): 头实体名称，默认为None
    relation (str, optional): 关系类型，默认为None
    tail_entity (str, optional): 尾实体名称，默认为None

    返回:
    networkx.DiGraph: 筛选后的子知识图谱
    """
    # 创建一个新的有向图用于存储子图
    subgraph = nx.DiGraph()

    # 遍历原始图中的所有边
    for u, v, data in G.edges(data=True):
        # 检查当前边是否符合筛选条件
        match_head = head_entity is None or u == head_entity
        match_relation = relation is None or data['relation'] == relation
        match_tail = tail_entity is None or v == tail_entity

        # 如果所有条件都满足，则添加这条边及其节点到子图中
        if match_head and match_relation and match_tail:
            # 添加头实体节点及其属性
            if u not in subgraph:
                subgraph.add_node(u, **G.nodes[u])
            # 添加尾实体节点及其属性
            if v not in subgraph:
                subgraph.add_node(v, **G.nodes[v])
            # 添加边及其属性
            subgraph.add_edge(u, v, **data)

    return subgraph


def show_graph(G, figsize=(15, 10), font_size=10, node_size=1200, edge_width=1.5, layout='spring', save_path=None):
    """
    显示知识图谱

    参数:
    G (networkx.DiGraph): 知识图谱有向图
    figsize (tuple): 图像尺寸
    font_size (int): 字体大小
    node_size (int): 节点大小
    edge_width (float): 边的宽度
    layout (str): 布局算法 ('spring', 'circular', 'kamada_kawai', 'planar', 'shell')
    """
    plt.figure(figsize=figsize)

    # 根据节点数量选择合适的布局算法和参数
    num_nodes = len(G.nodes())

    if layout == 'spring':
        # 使用spring布局，增加k值和迭代次数来减少重叠
        k_value = max(1.0, num_nodes * 0.3)  # 根据节点数量调整k值
        pos = nx.spring_layout(G, k=k_value, iterations=100, seed=42)
    elif layout == 'circular':
        pos = nx.circular_layout(G)
    elif layout == 'kamada_kawai':
        try:
            pos = nx.kamada_kawai_layout(G)
        except:
            # 如果kamada_kawai失败，回退到spring布局
            pos = nx.spring_layout(G, k=1.0, iterations=100, seed=42)
    elif layout == 'shell':
        pos = nx.shell_layout(G)
    else:
        # 默认使用改进的spring布局
        k_value = max(1.0, num_nodes * 0.3)
        pos = nx.spring_layout(G, k=k_value, iterations=100, seed=42)

    # 调整节点位置以避免重叠
    pos = adjust_positions_to_avoid_overlap(pos, node_size)

    # 获取节点属性
    node_labels = {node: f"{node}\n({G.nodes[node]['attribute']})" for node in G.nodes()}

    # 获取边属性（关系）
    edge_labels = {(u, v): G.edges[u, v]['relation'] for u, v in G.edges()}

    # 绘制节点
    nx.draw_networkx_nodes(G, pos, node_size=node_size, node_color='lightblue',
                          alpha=0.8, edgecolors='black', linewidths=1)

    # 绘制节点标签
    nx.draw_networkx_labels(G, pos, labels=node_labels, font_size=font_size,
                           font_family='SimHei', bbox=dict(boxstyle="round,pad=0.3",
                           facecolor="white", alpha=0.8))

    # 绘制有向边（箭头）
    nx.draw_networkx_edges(
        G, pos, width=edge_width, alpha=0.6, edge_color='gray',
        arrows=True, arrowsize=20, arrowstyle='->',
        connectionstyle="arc3,rad=0.1"  # 添加弧度避免边重叠
    )

    # 绘制边标签（关系）- 使用原始pos位置
    nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels,
                                font_size=font_size - 2, font_family='SimHei',
                                bbox=dict(boxstyle="round,pad=0.2", facecolor="yellow", alpha=0.7))

    plt.axis('off')
    plt.title('知识图谱可视化', fontsize=16, fontfamily='SimHei', pad=20)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图形已保存到: {save_path}")
    else:
        plt.show()


def adjust_positions_to_avoid_overlap(pos, node_size, min_distance=None):
    """
    调整节点位置以避免重叠

    参数:
    pos (dict): 节点位置字典
    node_size (int): 节点大小
    min_distance (float): 最小距离，如果为None则根据node_size自动计算

    返回:
    dict: 调整后的节点位置字典
    """
    import numpy as np

    if min_distance is None:
        # 根据节点大小计算最小距离
        min_distance = np.sqrt(node_size) * 0.01

    adjusted_pos = pos.copy()
    nodes = list(pos.keys())

    # 多次迭代调整位置
    for iteration in range(50):
        moved = False
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes[i+1:], i+1):
                x1, y1 = adjusted_pos[node1]
                x2, y2 = adjusted_pos[node2]

                # 计算两节点间距离
                distance = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)

                if distance < min_distance and distance > 0:
                    # 计算需要移动的距离
                    move_distance = (min_distance - distance) / 2

                    # 计算移动方向
                    dx = x2 - x1
                    dy = y2 - y1
                    length = np.sqrt(dx**2 + dy**2)

                    if length > 0:
                        # 单位向量
                        dx /= length
                        dy /= length

                        # 移动节点
                        adjusted_pos[node1] = (x1 - dx * move_distance, y1 - dy * move_distance)
                        adjusted_pos[node2] = (x2 + dx * move_distance, y2 + dy * move_distance)
                        moved = True

        # 如果没有节点需要移动，提前结束
        if not moved:
            break

    return adjusted_pos


def trans_graph(G):
    """
    将知识图谱转换为易于使用的字典列表格式

    参数:
    G (networkx.DiGraph): 知识图谱有向图

    返回:
    list: 字典列表，每个字典表示一条知识三元组
    """
    triples = []

    # 遍历图中的所有边
    for u, v, data in G.edges(data=True):
        # 获取头实体和尾实体的属性
        head_attr = G.nodes[u].get('attribute', '')
        tail_attr = G.nodes[v].get('attribute', '')

        # 构建三元组字典
        triple = {
            'head_entity': u,
            'head_attribute': head_attr,
            'relation': data.get('relation', ''),
            'tail_entity': v,
            'tail_attribute': tail_attr
        }

        triples.append(triple)

    return triples


# 示例用法
if __name__ == "__main__":
    # 假设CSV文件路径为'knowledge_graph.csv'
    file_path = 'diankeyihao.csv'

    # 读取知识图谱
    G = read_graph(file_path)

    # 显示知识图谱 - 可以尝试不同的布局算法
    print("生成完整知识图谱...")
    show_graph(G, layout='spring', save_path='full_graph.png')  # 保存完整图谱

    # 如果spring布局仍有重叠，可以尝试其他布局
    # print("尝试圆形布局...")
    show_graph(G, layout='circular', save_path='full_graph_circular.png')  # 圆形布局

    print("尝试Kamada-Kawai布局...")
    show_graph(G, layout='kamada_kawai', save_path='full_graph_kamada.png')  # 力导向布局
    
    print("生成筛选后的子图...")
    G1 = export_node(G, head_entity="电科一号", relation="包含")
    show_graph(G1, layout='spring', save_path='subgraph.png')  # 子图通常节点较少，spring布局效果更好

    a = trans_graph(G1)
    print("筛选结果:", len(a), "条记录")


