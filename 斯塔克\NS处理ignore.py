"""
作者: stark
日期: 2025年07月03日
"""
import os
import json
import cv2
import numpy as np
from tqdm import tqdm
import glob

# ===== 硬编码路径配置 =====
# 使用英文路径避免问题，或者使用原始字符串处理中文路径
IMAGE_DIR = r"G:\STARK\SODA\SODA-D\划分为yolo格式\images\test"  # 原始图片目录
JSON_PATH = r"G:\STARK\SODA\SODA-D\Annotations\test.json"  # JSON标注文件路径
FILLED_IMAGE_DIR = r"G:\STARK\SODA\SODA-D\NS处理\test图片"  # 填充后图片输出目录
YOLO_LABEL_DIR = r"G:\STARK\SODA\SODA-D\NS处理\test标签"  # YOLO格式标签输出目录
DEBUG_DIR = os.path.join(FILLED_IMAGE_DIR, "debug_masks")  # 调试掩码输出目录

# 创建输出目录
os.makedirs(FILLED_IMAGE_DIR, exist_ok=True)
os.makedirs(YOLO_LABEL_DIR, exist_ok=True)
os.makedirs(DEBUG_DIR, exist_ok=True)

# ===== 填充参数配置 =====
PADDING = 2  # 保留区域的扩展像素
INPAINT_RADIUS = 3  # 修补半径
INPAINT_METHOD = cv2.INPAINT_NS  # 使用NS算法 (Navier-Stokes)
BLUR_SIZE = 5  # 掩码边缘模糊处理大小


def expand_bbox(bbox, img_width, img_height):
    """扩展边界框并确保在图像范围内"""
    x, y, w, h = bbox

    # 计算扩展后的坐标
    x1 = max(0, int(x - PADDING))
    y1 = max(0, int(y - PADDING))
    x2 = min(img_width, int(x + w + PADDING))
    y2 = min(img_height, int(y + h + PADDING))

    return [x1, y1, x2 - x1, y2 - y1]


def apply_inpaint(img, mask):
    """
    应用NS算法进行图像修复，带边缘模糊处理
    参数:
        img - 输入图像 (BGR格式)
        mask - 单通道掩码 (0=保留, 255=填充)
    返回:
        修复后的图像
    """
    # 模糊处理掩码边缘以获得更平滑的过渡
    if BLUR_SIZE > 0:
        kernel = np.ones((BLUR_SIZE, BLUR_SIZE), np.uint8)
        dilated_mask = cv2.dilate(mask, kernel, iterations=1)
        blurred_mask = cv2.GaussianBlur(dilated_mask, (BLUR_SIZE, BLUR_SIZE), 0)
    else:
        blurred_mask = mask

    # 应用NS算法
    return cv2.inpaint(img, blurred_mask, INPAINT_RADIUS, INPAINT_METHOD)


def safe_imread(img_path):
    """安全读取图像，避免中文路径问题"""
    try:
        # 方法1: 使用二进制读取
        with open(img_path, 'rb') as f:
            img_array = np.frombuffer(f.read(), dtype=np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        return img
    except Exception as e:
        print(f"⚠️ 无法读取图片: {img_path}, 错误: {str(e)}")
        return None


def safe_imwrite(img_path, img):
    """安全保存图像，避免中文路径问题"""
    try:
        # 获取文件扩展名
        ext = os.path.splitext(img_path)[1].lower()
        if ext == '.png':
            encode_param = [cv2.IMWRITE_PNG_COMPRESSION, 5]
        elif ext in ('.jpg', '.jpeg'):
            encode_param = [cv2.IMWRITE_JPEG_QUALITY, 95]
        else:
            encode_param = []

        # 使用imencode避免路径问题
        success, encoded_img = cv2.imencode(ext, img, encode_param)
        if success:
            with open(img_path, 'wb') as f:
                encoded_img.tofile(f)
            return True
        return False
    except Exception as e:
        print(f"⚠️ 无法保存图片: {img_path}, 错误: {str(e)}")
        return False


def process_image(image_info, annotations, all_categories):
    """处理单张图片的填充和标签转换"""
    # 读取图片
    img_path = os.path.join(IMAGE_DIR, image_info["file_name"])
    img = safe_imread(img_path)
    if img is None:
        return

    h, w = img.shape[:2]

    # 创建掩码（0=保留，255=填充）
    fill_mask = np.zeros((h, w), dtype=np.uint8)
    yolo_lines = []

    # 第一步：标记所有需要填充的区域 (ignore=1)
    for ann in annotations:
        if ann["ignore"] == 1:
            bbox = ann["bbox"]
            x, y, w_bbox, h_bbox = map(int, bbox)
            fill_mask[y:y + h_bbox, x:x + w_bbox] = 255

    # 第二步：标记所有保留区域 (ignore=0)，覆盖填充区域
    for ann in annotations:
        if ann["ignore"] == 0:
            bbox = ann["bbox"]
            category_id = ann["category_id"]

            # 扩展边界框
            expanded_bbox = expand_bbox(bbox, w, h)
            x_exp, y_exp, w_exp, h_exp = map(int, expanded_bbox)

            # 在扩展区域内创建保留区（0=保留）
            fill_mask[y_exp:y_exp + h_exp, x_exp:x_exp + w_exp] = 0

            # 准备YOLO格式标签
            x, y, w_bbox, h_bbox = map(int, bbox)
            x_center = (x + w_bbox / 2) / w
            y_center = (y + h_bbox / 2) / h
            width_norm = w_bbox / w
            height_norm = h_bbox / h

            # YOLO类别ID（原始类别ID减1）
            yolo_class = category_id - 1
            yolo_lines.append(f"{yolo_class} {x_center:.6f} {y_center:.6f} {width_norm:.6f} {height_norm:.6f}")

    # 应用NS算法进行图像修复
    filled_img = apply_inpaint(img, fill_mask)

    # 保存处理后的图片
    output_img_path = os.path.join(FILLED_IMAGE_DIR, image_info["file_name"])
    if not safe_imwrite(output_img_path, filled_img):
        print(f"⚠️ 无法保存填充后的图片: {output_img_path}")

    # 保存调试掩码
    debug_mask_path = os.path.join(DEBUG_DIR, f"mask_{image_info['file_name']}")
    debug_mask_path = os.path.splitext(debug_mask_path)[0] + ".png"  # 确保保存为PNG格式
    if not safe_imwrite(debug_mask_path, fill_mask):
        print(f"⚠️ 无法保存调试掩码: {debug_mask_path}")

    # 保存YOLO格式标签
    if yolo_lines:
        label_filename = os.path.splitext(image_info["file_name"])[0] + ".txt"
        label_path = os.path.join(YOLO_LABEL_DIR, label_filename)
        try:
            with open(label_path, "w", encoding="utf-8") as f:
                f.write("\n".join(yolo_lines))
        except Exception as e:
            print(f"⚠️ 无法保存YOLO标签: {label_path}, 错误: {str(e)}")


def main():
    # 加载JSON数据
    try:
        with open(JSON_PATH, "r", encoding="utf-8") as f:
            data = json.load(f)
    except Exception as e:
        print(f"⚠️ 无法加载JSON文件: {JSON_PATH}, 错误: {str(e)}")
        return

    # 创建image_id到image_info的映射
    image_map = {img["id"]: img for img in data["images"]}

    # 创建image_id到annotations列表的映射
    ann_map = {}
    for ann in data["annotations"]:
        img_id = ann["image_id"]
        if img_id not in ann_map:
            ann_map[img_id] = []
        ann_map[img_id].append(ann)

    # 处理每张图片
    print(f"🚀 开始处理 {len(data['images'])} 张图片...")
    print(f"⚙️ 使用填充算法: Navier-Stokes (INPAINT_NS)")
    print(f"⚙️ 填充半径: {INPAINT_RADIUS} 像素")
    print(f"⚙️ 边缘模糊: {BLUR_SIZE} 像素")
    print(f"⚙️ 保留区域扩展: {PADDING} 像素")

    for img_info in tqdm(data["images"], desc="处理图片"):
        img_id = img_info["id"]
        annotations = ann_map.get(img_id, [])
        process_image(img_info, annotations, data["categories"])

    print(f"✅ 处理完成！结果保存在:")
    print(f"   填充图片: {FILLED_IMAGE_DIR}")
    print(f"   YOLO标签: {YOLO_LABEL_DIR}")
    print(f"   调试掩码: {DEBUG_DIR}")

    # 验证结果
    filled_count = len(glob.glob(os.path.join(FILLED_IMAGE_DIR, "*.*")))
    print(f"  生成填充图片: {filled_count}/{len(data['images'])}")

    label_count = len(glob.glob(os.path.join(YOLO_LABEL_DIR, "*.txt")))
    print(f"  生成YOLO标签: {label_count}/{len(data['images'])}")


if __name__ == "__main__":
    main()