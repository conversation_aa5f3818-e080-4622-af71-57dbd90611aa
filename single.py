import cv2
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import entropy
import pandas as pd
from datetime import datetime

def calculate_features(image):
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Edge detection using Canny
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.mean(edges > 0)
    
    # Structure tensor analysis
    sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    structure_tensor = np.sqrt(sobelx**2 + sobely**2)
    structure_density = np.mean(structure_tensor)
    
    # Entropy calculation
    hist, _ = np.histogram(gray.flatten(), bins=256, range=[0,256])
    image_entropy = entropy(hist / hist.sum())
    
    # Grayscale mean and contrast
    gray_mean = np.mean(gray)
    gray_std = np.std(gray)
    
    return {
        'edge_density': edge_density,
        'structure_density': structure_density,
        'entropy': image_entropy,
        'gray_mean': gray_mean,
        'gray_std': gray_std
    }

def plot_features(features, ax_bar, ax_pie):
    labels = list(features.keys())
    values = list(features.values())
    
    # Bar chart for features
    ax_bar.clear()
    ax_bar.bar(labels, values, color='skyblue')
    ax_bar.set_title('Feature Values')
    ax_bar.set_ylabel('Value')
    
    # Pie chart for total weight distribution
    weights = [v / sum(values) for v in values]
    ax_pie.clear()
    ax_pie.pie(weights, labels=labels, autopct='%1.1f%%', startangle=90)
    ax_pie.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle.
    ax_pie.set_title('Weight Distribution')

def determine_scene_type(features):
    if features['gray_mean'] < 80:
        return 'Night'
    else:
        return 'Day'

def process_frame(frame, ax_bar, ax_pie):
    features = calculate_features(frame)
    scene_type = determine_scene_type(features)
    
    plot_features(features, ax_bar, ax_pie)
    plt.pause(0.01)
    
    return features, scene_type

def main(video_path):
    cap = cv2.VideoCapture(video_path)
    fig, (ax_img, ax_bar, ax_pie) = plt.subplots(1, 3, figsize=(18, 6))
    plt.ion()  # Turn on interactive mode
    
    data = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        features, scene_type = process_frame(frame, ax_bar, ax_pie)
        
        # Display the original frame with feature annotations
        ax_img.imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        ax_img.set_title(f'Scene Type: {scene_type}')
        ax_img.axis('off')
        
        plt.draw()
        
        # Save data to CSV
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        data.append([timestamp] + list(features.values()) + [scene_type])
    
    cap.release()
    plt.ioff()
    plt.show()
    
    df = pd.DataFrame(data, columns=['Timestamp', 'Edge Density', 'Structure Density', 
                                     'Entropy', 'Gray Mean', 'Gray Std', 'Scene Type'])
    df.to_csv('feature_data.csv', index=False)

if __name__ == "__main__":
    video_path = 'V_video/0002_V.mp4'  # Replace with your video path
    main(video_path)



