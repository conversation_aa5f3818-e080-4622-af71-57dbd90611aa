import cv2
import numpy as np
import os
from typing import Union, List
from skimage.feature import local_binary_pattern
import torch
import matplotlib.pyplot as plt
from PIL import Image
from ultralytics import YOLO

class ImageProcessor:
    def __init__(self, output_dir):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

    def process_image(self, img_path):
        print(f"处理图像: {img_path}")
        img = cv2.imread(img_path)
        if img is None:
            print(f"未找到图像: {img_path}")
            return
        # 去噪
        denoised = cv2.medianBlur(img, 3)
        # 锐化
        kernel = np.array([[0, -1, 0],
                           [-1, 5, -1],
                           [0, -1, 0]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        # 直方图均衡化
        img_yuv = cv2.cvtColor(sharpened, cv2.COLOR_BGR2YUV)
        img_yuv[:, :, 0] = cv2.equalizeHist(img_yuv[:, :, 0])
        equalized = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2BGR)
        # 保存
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        print(f"baocundizhi{self.output_dir}")
        cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_denoised.jpg"), denoised)
        cv2.imwrite(os.path.join(self.output_dir,f"{base_name}_sharpened.jpg"), sharpened)
        cv2.imwrite(os.path.join(self.output_dir,f"{base_name}_equalized.jpg"), equalized)

    @staticmethod
    def process_ImageProcessor(inputs: Union[str, List[str]], output_dir: str):
        processor = ImageProcessor(output_dir)
        if isinstance(inputs, str):
            if os.path.isfile(inputs):
                print(f"zheliu{inputs} ")
                processor.process_image(inputs)
            elif os.path.isdir(inputs):
                for file in os.listdir(inputs):
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                        img_path = os.path.join(inputs, file)
                        processor.process_image(img_path)
            else:
                print("输入路径既不是文件也不是文件夹！")
        elif isinstance(inputs, list):
            for img_path in inputs:
                processor.process_image(img_path)
        else:
            print("输入类型不支持！")



class FeatureExtractor:
    def __init__(self, output_dir):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.yolo_model='D:\\vscode_mission\\yolov11\\yolov8n.pt'
        

    def extract_and_save(self, img_path):
        print(f"处理图像: {img_path}")
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        print("img_path",img_path)
        if img is None:
            print(f"未找到图像: {img_path}")
            return
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        all_path=[] 

        # Harris角点检测

        harris_img = img.copy()
        harris_corners = cv2.cornerHarris(np.float32(harris_img), 2, 3, 0.04)
        # 归一化并阈值
        harris_img_color = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        harris_img_color[harris_corners > 0.01 * harris_corners.max()] = [0, 0, 255]
        cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_harris.jpg"), harris_img_color)
        print(f"Harris角点特征提取成功: {base_name}.jpg")
        all_path.append(os.path.join(self.output_dir, f"{base_name}_harris.jpg"))



        # Canny 边缘检测

        # 读取原始灰度图像（已在 extract_and_save 方法中加载）
        edge_img = img.copy()
        # 使用 Canny 算法提取边缘
        edges = cv2.Canny(edge_img, threshold1=100, threshold2=200)
        # 保存结果图像
        output_path = os.path.join(self.output_dir, f"{base_name}_canny.jpg")
        success = cv2.imwrite(output_path, edges)

        all_path.append(output_path)
        if success:
            print(f"边缘特征提取成功: {base_name}_edge.jpg")
        else:
            print(f"无法保存边缘特征图像: {base_name}_edge.jpg")

        # BRISK特征

        brisk = cv2.BRISK_create()
        kp_brisk, des_brisk = brisk.detectAndCompute(img, None)
        brisk_img = cv2.drawKeypoints(img, kp_brisk, None, color=(255,0,0), flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)
        cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_brisk.jpg"), brisk_img)
        print(f"BRISK特征提取成功: {base_name}.jpg")
        all_path.append(os.path.join(self.output_dir, f"{base_name}_brisk.jpg"))
        # np.save(os.path.join(self.output_dir, f"{base_name}_brisk_kp.npy"), np.array([k.pt for k in kp_brisk]))
        # np.save(os.path.join(self.output_dir, f"{base_name}_brisk_des.npy"), des_brisk)


        # SIFT角点特征

        # 重新读取图像为灰度图（确保未被修改）
        img_sift = img.copy()
        if img_sift is None:
            print(f"SIFT: 图像未找到 - {img_path}")

        # 创建SIFT检测器
        sift = cv2.SIFT_create()

        # 检测关键点并计算描述子
        keypoints, descriptors = sift.detectAndCompute(img_sift, None)

        if keypoints is None or len(keypoints) == 0:
            print(f"SIFT: 未检测到关键点 - {img_path}")
        # 可视化关键点
        sift_img = cv2.drawKeypoints(img_sift, keypoints, None,
                                    flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS,
                                    color=(255, 0, 0))  # 蓝色关键点
        # 保存可视化图像
        cv2.imwrite(os.path.join(self.output_dir, f"{base_name}_sift.jpg"), sift_img)
        all_path.append(os.path.join(self.output_dir, f"{base_name}_sift.jpg"))
        print(f"SIFT特征提取成功: {base_name}.jpg")

        # ORB 特征提取

        # 创建 ORB 检测器
        orb = cv2.ORB_create()

        # 检测关键点并计算描述子
        keypoints, descriptors = orb.detectAndCompute(img, None)

        if keypoints is None or len(keypoints) == 0:
            print(f"ORB: 未检测到关键点 - {base_name}")
        else:
            # 可视化关键点
            orb_img = cv2.drawKeypoints(img, keypoints, None,
                                    flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS,
                                    color=(255, 0, 0))  # 蓝色关键点

            # 保存可视化图像
            output_path = os.path.join(self.output_dir, f"{base_name}_orb.jpg")
            success = cv2.imwrite(output_path, orb_img)
            all_path.append(output_path)

            if success:
                print(f"ORB特征提取成功: {base_name}_orb.jpg")
            else:
                print(f"无法保存 ORB 特征图像: {base_name}_orb.jpg")
        return all_path




    @staticmethod
    def process_FeatureExtractor(inputs: Union[str, List[str]], output_dir: str):
        processor = FeatureExtractor(output_dir)
        if isinstance(inputs, str):
            if os.path.isfile(inputs):
                print(f"处理单个文件: {inputs}")
                a = processor.extract_and_save(inputs)
                return a
            elif os.path.isdir(inputs):
                print(f"处理文件夹: {inputs}")
                for root, dirs, files in os.walk(inputs):
                    for file in files:
                        if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                            img_path = os.path.join(root, file)
                            print(f"处理图像: {img_path}")
                            a = processor.extract_and_save(img_path)
                            return a
            else:
                print("输入路径既不是文件也不是文件夹！")
        elif isinstance(inputs, list):
            for img_path in inputs:
                if os.path.isfile(img_path):
                    print(f"处理列表中的文件: {img_path}")
                    a = processor.extract_and_save(img_path)
                    return a
                else:
                    print(f"跳过不存在的文件: {img_path}")
        else:
            print("输入类型不支持！")

class FeatureSelector:
    """
    特征识别有效性筛选类，支持多种筛选方法，并可自动选出最优图片
    """
    def __init__(self, min_keypoints=10, min_response=0.01, min_area_ratio=0.3):
        """
        :param min_keypoints: 有效特征点最小数量
        :param min_response: 有效特征点最小响应值（适用于SIFT/ORB等）
        :param min_area_ratio: 特征点分布的最小覆盖面积比例（0~1）
        """
        self.min_keypoints = min_keypoints
        self.min_response = min_response
        self.min_area_ratio = min_area_ratio

    def score_image(self, img_path, method='sift'):
        """
        计算单张图片的特征有效性得分（可自定义加权）
        """
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            return 0
        if method == 'sift' or method == 'asift':
            detector = cv2.SIFT_create()
        elif method == 'brisk':
            detector = cv2.BRISK_create()
        else:
            return 0

        kp, des = detector.detectAndCompute(img, None)
        if kp is None or len(kp) == 0 or des is None:
            return 0

        # 评分项
        num_kp = len(kp)
        max_response = max([point.response for point in kp if hasattr(point, 'response')], default=0)
        # 分布面积
        points = np.array([k.pt for k in kp])
        x_min, y_min = points.min(axis=0)
        x_max, y_max = points.max(axis=0)
        area = (x_max - x_min) * (y_max - y_min)
        img_area = img.shape[0] * img.shape[1]
        area_ratio = area / img_area if img_area > 0 else 0

        # 你可以自定义加权方式
        score = (
            num_kp * 1.0 +          # 特征点数量
            max_response * 100.0 +  # 响应值
            area_ratio * 100.0      # 分布面积比例
        )
        return score

    def select_best_image(self, folder, method='sift'):
        """
        在文件夹中筛选出特征识别有效性最强的图片
        :param folder: 文件夹路径
        :param method: 特征类型
        :return: 最优图片路径
        """
        best_score = -1
        best_img = None
        for file in os.listdir(folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                img_path = os.path.join(folder, file)
                score = self.score_image(img_path, method=method)
                if score > best_score:
                    best_score = score
                    best_img = img_path
        if best_img:
            print(f"最优图片: {best_img}，得分: {best_score}")
        else:
            print("未找到有效图片")
        return best_img



    @staticmethod
    def process_inputs(inputs: Union[str, List[str]], output_dir: str):
        extractor = FeatureExtractor(output_dir)
        if isinstance(inputs, str):
            if os.path.isfile(inputs):
                extractor.extract_and_save(inputs)
            elif os.path.isdir(inputs):
                for file in os.listdir(inputs):
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')):
                        img_path = os.path.join(inputs, file)
                        extractor.extract_and_save(img_path)
            else:
                print("输入路径既不是文件也不是文件夹！")
        elif isinstance(inputs, list):
            for img_path in inputs:
                extractor.extract_and_save(img_path)
        else:
            print("输入类型不支持！")