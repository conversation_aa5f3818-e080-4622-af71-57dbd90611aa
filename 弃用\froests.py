import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFileDialog, QSlider, 
                            QComboBox, QGroupBox, QGridLayout, QMessageBox)
from PyQt5.QtGui import QImage, QPixmap, QFont
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from datetime import datetime
import os
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# 设置matplotlib中文字体
plt.rcParams["font.family"] = ["SimHei"]
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class FeatureExtractor:
    """特征提取器，用于从图像中提取边缘、结构、信息熵、灰度和对比度等特征"""
    
    @staticmethod
    def extract_edges(image):
        """提取图像边缘特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        blur = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blur, 50, 150)
        edge_density = np.count_nonzero(edges) / edges.size
        return edges, edge_density
    #计算图像高频部分的复杂度和能量
    @staticmethod
    def extract_high_frequency_complexity(image):
        """提取图像高频部分的复杂度和能量"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        fft = np.fft.fft2(gray)
        fft_shift = np.fft.fftshift(fft)
        magnitude_spectrum = 20 * np.log(np.abs(fft_shift))
        # 计算高频部分的复杂度和能量
        high_freq_mask = np.zeros_like(magnitude_spectrum)
        h, w = magnitude_spectrum.shape
        high_freq_mask[h//2-30:h//2+30, w//2-30:w//2+30] = 1  # 只保留高频部分
        high_freq_spectrum = magnitude_spectrum * high_freq_mask
        complexity = np.std(high_freq_spectrum)
        energy = np.sum(high_freq_spectrum ** 2)
        return complexity, energy
    
    @staticmethod
    def extract_structure(image):
        """提取图像结构特征"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        # 使用拉普拉斯算子检测图像的结构复杂性
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        structure_complexity = np.var(laplacian)
        return laplacian, structure_complexity
    
    @staticmethod
    def calculate_entropy(image):
        """计算图像信息熵"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist_norm = hist.ravel() / hist.sum()
        non_zero = hist_norm[hist_norm > 0]
        entropy = -np.sum(non_zero * np.log2(non_zero))
        return entropy
    
    @staticmethod
    def calculate_contrast(image):
        """计算图像对比度"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        # 计算标准差作为对比度的度量
        contrast = np.std(gray)
        return contrast
    
    @staticmethod
    def calculate_brightness(image):
        """计算图像平均灰度"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        brightness = np.mean(gray)
        return brightness
    
    @staticmethod
    def extract_all_features(image):
        """提取所有特征并返回特征向量"""
        edges, edge_density = FeatureExtractor.extract_edges(image)
        structure, structure_complexity = FeatureExtractor.extract_structure(image)
        entropy = FeatureExtractor.calculate_entropy(image)
        contrast = FeatureExtractor.calculate_contrast(image)
        brightness = FeatureExtractor.calculate_brightness(image)
        
        return {
            'edges': edges,
            'edge_density': edge_density,
            'structure': structure,
            'structure_complexity': structure_complexity,
            'entropy': entropy,
            'contrast': contrast,
            'brightness': brightness
        }


class WeightCalculator:
    """权重计算器，根据特征计算可见光和红外的权重"""
    
    @staticmethod
    def calculate_weights(visible_features, ir_features):
        """计算可见光和红外图像的权重"""
        # 定义各特征的重要性权重
        feature_weights = {
            'edge_density': 0.1,
            'structure_complexity': 0.2,
            'entropy': 0.1,
            'contrast': 0.2,
            'brightness': 0.2,
            'high_frequency_complexity': 0.1,
            'high_frequency_energy': 0.1
        }
        
        # 计算每种特征下可见光和红外的得分
        scores = {}
        for feature in feature_weights:
            # 对于亮度特征，需要特殊处理，因为白天和夜晚的要求不同
            if feature == 'brightness':
                # 判断场景是白天还是夜晚
                is_day = visible_features['brightness'] > 100
                
                if is_day:
                    # 白天：可见光图像的亮度应该更高
                    visible_score = visible_features[feature] / max(visible_features[feature], ir_features[feature])
                    ir_score = ir_features[feature] / max(visible_features[feature], ir_features[feature])
                else:
                    # 夜晚：红外图像的亮度应该更高
                    visible_score = ir_features[feature] / max(visible_features[feature], ir_features[feature])
                    ir_score = visible_features[feature] / max(visible_features[feature], ir_features[feature])
            else:
                # 其他特征：值越高越好
                visible_score = visible_features[feature] / max(visible_features[feature], ir_features[feature])
                ir_score = ir_features[feature] / max(visible_features[feature], ir_features[feature])
            
            scores[f'visible_{feature}'] = visible_score
            scores[f'ir_{feature}'] = ir_score
        
        # 计算最终权重
        visible_weight = sum(scores[f'visible_{feature}'] * weight for feature, weight in feature_weights.items())
        ir_weight = sum(scores[f'ir_{feature}'] * weight for feature, weight in feature_weights.items())
        
        # 归一化权重
        total_weight = visible_weight + ir_weight
        visible_weight_normalized = visible_weight / total_weight if total_weight > 0 else 0.5
        ir_weight_normalized = ir_weight / total_weight if total_weight > 0 else 0.5
        
        # 判断场景类型
        is_day = visible_features['brightness'] > ir_features['brightness']
        
        return {
            'visible_weight': visible_weight_normalized,
            'ir_weight': ir_weight_normalized,
            'is_day': is_day,
            'feature_scores': scores
        }


class ImageProcessorThread(QThread):
    """图像处理线程，用于在后台处理图像"""
    update_signal = pyqtSignal(dict)
    
    def __init__(self, visible_frame, ir_frame):
        super().__init__()
        self.visible_frame = visible_frame
        self.ir_frame = ir_frame
        self.feature_extractor = FeatureExtractor()
        self.weight_calculator = WeightCalculator()
        
    def run(self):
        # 提取特征
        visible_features = self.feature_extractor.extract_all_features(self.visible_frame)
        ir_features = self.feature_extractor.extract_all_features(self.ir_frame)
        
        # 计算权重
        weights = self.weight_calculator.calculate_weights(visible_features, ir_features)
        
        # 准备更新数据
        update_data = {
            'visible_features': visible_features,
            'ir_features': ir_features,
            'weights': weights
        }
        
        # 发送更新信号
        self.update_signal.emit(update_data)


class VideoThread(QThread):
    """视频处理线程，用于实时处理视频流"""
    update_signal = pyqtSignal(dict)
    
    def __init__(self, visible_video_path, ir_video_path):
        super().__init__()
        self.visible_video_path = visible_video_path
        self.ir_video_path = ir_video_path
        self.feature_extractor = FeatureExtractor()
        self.weight_calculator = WeightCalculator()
        self.running = True
        
    def run(self):
        # 打开视频文件
        visible_cap = cv2.VideoCapture(self.visible_video_path)
        ir_cap = cv2.VideoCapture(self.ir_video_path)
        
        # 检查视频是否成功打开
        if not visible_cap.isOpened() or not ir_cap.isOpened():
            self.update_signal.emit({'error': '无法打开视频文件'})
            return
        
        # 获取视频帧率
        fps = visible_cap.get(cv2.CAP_PROP_FPS)
        frame_delay = int(1000 / fps)
        
        while self.running:
            # 读取视频帧
            ret_visible, visible_frame = visible_cap.read()
            ret_ir, ir_frame = ir_cap.read()
            
            # 如果任一视频读取完毕，则退出循环
            if not ret_visible or not ret_ir:
                break
            
            # 调整红外图像大小以匹配可见光图像
            if visible_frame.shape[:2] != ir_frame.shape[:2]:
                ir_frame = cv2.resize(ir_frame, (visible_frame.shape[1], visible_frame.shape[0]))
            
            # 提取特征
            visible_features = self.feature_extractor.extract_all_features(visible_frame)
            ir_features = self.feature_extractor.extract_all_features(ir_frame)
            
            # 计算权重
            weights = self.weight_calculator.calculate_weights(visible_features, ir_features)
            
            # 准备更新数据
            update_data = {
                'visible_frame': visible_frame,
                'ir_frame': ir_frame,
                'visible_features': visible_features,
                'ir_features': ir_features,
                'weights': weights
            }
            
            # 发送更新信号
            self.update_signal.emit(update_data)
            
            # 控制处理速度
            self.msleep(frame_delay)
        
        # 释放资源
        visible_cap.release()
        ir_cap.release()
        
        # 发送处理完成信号
        self.update_signal.emit({'finished': True})
    
    def stop(self):
        """停止视频处理线程"""
        self.running = False
        self.wait()


class Canvas(FigureCanvas):
    """Matplotlib画布，用于在Qt界面中显示图表"""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)
        self.axes = self.fig.add_subplot(111)
        self.fig.tight_layout()


class FeatureVisualizer(QWidget):
    """特征可视化界面组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建特征柱状图
        self.feature_canvas = Canvas(self, width=8, height=4)
        layout.addWidget(self.feature_canvas)
        
        # 创建权重饼图
        self.weight_canvas = Canvas(self, width=8, height=4)
        layout.addWidget(self.weight_canvas)
        
        # 场景类型标签
        self.scene_label = QLabel("场景类型: 未知")
        self.scene_label.setFont(QFont("SimHei", 12))
        layout.addWidget(self.scene_label)
        
        # 初始化图表
        self.update_feature_chart({}, {})
        self.update_weight_pie(0.5, 0.5)
        
    def update_feature_chart(self, visible_features, ir_features):
        """更新特征柱状图"""
        self.feature_canvas.axes.clear()
        
        if not visible_features or not ir_features:
            self.feature_canvas.axes.set_title("特征对比")
            self.feature_canvas.axes.set_xlabel("特征")
            self.feature_canvas.axes.set_ylabel("值")
            self.feature_canvas.draw()
            return
        
        # 定义要显示的特征
        features = ['edge_density', 'structure_complexity', 'entropy', 'contrast', 'brightness']
        feature_names = ['边缘密度', '结构复杂度', '信息熵', '对比度', '亮度']
        
        # 提取特征值
        visible_values = [visible_features[feature] for feature in features]
        ir_values = [ir_features[feature] for feature in features]
        
        # 设置柱状图
        x = np.arange(len(features))
        width = 0.35
        
        rects1 = self.feature_canvas.axes.bar(x - width/2, visible_values, width, label='可见光')
        rects2 = self.feature_canvas.axes.bar(x + width/2, ir_values, width, label='红外')
        
        # 设置图表属性
        self.feature_canvas.axes.set_ylabel('特征值')
        self.feature_canvas.axes.set_title('特征对比')
        self.feature_canvas.axes.set_xticks(x)
        self.feature_canvas.axes.set_xticklabels(feature_names)
        self.feature_canvas.axes.legend()
        
        # 添加数值标签
        def autolabel(rects):
            for rect in rects:
                height = rect.get_height()
                self.feature_canvas.axes.annotate('{0:.2f}'.format(height),
                    xy=(rect.get_x() + rect.get_width() / 2, height),
                    xytext=(0, 3),  # 3 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom')
        
        autolabel(rects1)
        autolabel(rects2)
        
        self.feature_canvas.fig.tight_layout()
        self.feature_canvas.draw()
    
    def update_weight_pie(self, visible_weight, ir_weight):
        """更新权重饼图"""
        self.weight_canvas.axes.clear()
        
        # 设置饼图数据
        labels = '可见光', '红外'
        sizes = [visible_weight, ir_weight]
        colors = ['lightskyblue', 'lightcoral']
        explode = (0.1, 0)  # 突出显示可见光部分
        
        # 绘制饼图
        self.weight_canvas.axes.pie(sizes, explode=explode, labels=labels, colors=colors,
                autopct='%1.1f%%', shadow=True, startangle=90)
        self.weight_canvas.axes.axis('equal')  # 保证饼图是圆的
        self.weight_canvas.axes.set_title('探测体制权重分配')
        
        self.weight_canvas.fig.tight_layout()
        self.weight_canvas.draw()
    
    def update_scene_label(self, is_day):
        """更新场景类型标签"""
        scene_type = "白天" if is_day else "夜晚"
        self.scene_label.setText(f"场景类型: {scene_type}")


class FeatureDifferenceVisualizer(QWidget):
    """特征差异可视化界面组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建特征差异表格
        self.create_feature_difference_grid()
        layout.addWidget(self.feature_grid_group)
        
        # 创建特征图像显示区域
        self.create_feature_images_layout()
        layout.addLayout(self.feature_images_layout)
        
    def create_feature_difference_grid(self):
        """创建特征差异表格"""
        self.feature_grid_group = QGroupBox("特征差异")
        grid_layout = QGridLayout()
        
        # 表头
        headers = ["特征", "可见光", "红外", "差异"]
        for col, header in enumerate(headers):
            label = QLabel(header)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("font-weight: bold;")
            grid_layout.addWidget(label, 0, col)
        
        # 创建特征行
        self.feature_labels = {}
        features = ["边缘密度", "结构复杂度", "信息熵", "对比度", "亮度"]
        for row, feature in enumerate(features, 1):
            # 特征名称
            grid_layout.addWidget(QLabel(feature), row, 0)
            
            # 可见光值
            visible_label = QLabel("0.00")
            visible_label.setAlignment(Qt.AlignCenter)
            grid_layout.addWidget(visible_label, row, 1)
            self.feature_labels[f"visible_{feature}"] = visible_label
            
            # 红外值
            ir_label = QLabel("0.00")
            ir_label.setAlignment(Qt.AlignCenter)
            grid_layout.addWidget(ir_label, row, 2)
            self.feature_labels[f"ir_{feature}"] = ir_label
            
            # 差异值
            diff_label = QLabel("0.00")
            diff_label.setAlignment(Qt.AlignCenter)
            grid_layout.addWidget(diff_label, row, 3)
            self.feature_labels[f"diff_{feature}"] = diff_label
        
        self.feature_grid_group.setLayout(grid_layout)
    
    def create_feature_images_layout(self):
        """创建特征图像显示区域"""
        self.feature_images_layout = QGridLayout()
        
        # 可见光原始图像
        self.visible_label = QLabel("可见光图像")
        self.visible_label.setAlignment(Qt.AlignCenter)
        self.feature_images_layout.addWidget(self.visible_label, 0, 0)
        
        # 红外原始图像
        self.ir_label = QLabel("红外图像")
        self.ir_label.setAlignment(Qt.AlignCenter)
        self.feature_images_layout.addWidget(self.ir_label, 0, 1)
        
        # 边缘特征
        self.visible_edges_label = QLabel("可见光边缘")
        self.visible_edges_label.setAlignment(Qt.AlignCenter)
        self.feature_images_layout.addWidget(self.visible_edges_label, 1, 0)
        
        self.ir_edges_label = QLabel("红外边缘")
        self.ir_edges_label.setAlignment(Qt.AlignCenter)
        self.feature_images_layout.addWidget(self.ir_edges_label, 1, 1)
        
        # 结构特征
        self.visible_structure_label = QLabel("可见光结构")
        self.visible_structure_label.setAlignment(Qt.AlignCenter)
        self.feature_images_layout.addWidget(self.visible_structure_label, 2, 0)
        
        self.ir_structure_label = QLabel("红外结构")
        self.ir_structure_label.setAlignment(Qt.AlignCenter)
        self.feature_images_layout.addWidget(self.ir_structure_label, 2, 1)
    
    def update_feature_differences(self, visible_features, ir_features):
        """更新特征差异表格"""
        if not visible_features or not ir_features:
            return
        
        feature_mapping = {
            "边缘密度": "edge_density",
            "结构复杂度": "structure_complexity",
            "信息熵": "entropy",
            "对比度": "contrast",
            "亮度": "brightness"
        }
        
        for display_name, feature_name in feature_mapping.items():
            # 获取特征值
            visible_value = visible_features[feature_name]
            ir_value = ir_features[feature_name]
            diff_value = abs(visible_value - ir_value)
            
            # 更新标签
            self.feature_labels[f"visible_{display_name}"].setText(f"{visible_value:.2f}")
            self.feature_labels[f"ir_{display_name}"].setText(f"{ir_value:.2f}")
            self.feature_labels[f"diff_{display_name}"].setText(f"{diff_value:.2f}")
    
    def update_feature_images(self, visible_frame, ir_frame, visible_features, ir_features):
        """更新特征图像显示"""
        # 转换OpenCV图像为Qt可显示格式
        def convert_to_qimage(img):
            if len(img.shape) == 3:
                rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_img.shape
                bytes_per_line = ch * w
                return QImage(rgb_img.data, w, h, bytes_per_line, QImage.Format_RGB888)
            else:
                h, w = img.shape
                return QImage(img.data, w, h, w, QImage.Format_Grayscale8)
        
        # 原始图像
        if visible_frame is not None:
            visible_qimg = convert_to_qimage(visible_frame)
            pixmap = QPixmap.fromImage(visible_qimg)
            scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio)
            self.visible_label.setPixmap(scaled_pixmap)
        
        if ir_frame is not None:
            ir_qimg = convert_to_qimage(ir_frame)
            pixmap = QPixmap.fromImage(ir_qimg)
            scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio)
            self.ir_label.setPixmap(scaled_pixmap)
        
        # 边缘特征
        if 'edges' in visible_features:
            visible_edges_qimg = convert_to_qimage(visible_features['edges'])
            pixmap = QPixmap.fromImage(visible_edges_qimg)
            scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio)
            self.visible_edges_label.setPixmap(scaled_pixmap)
        
        if 'edges' in ir_features:
            ir_edges_qimg = convert_to_qimage(ir_features['edges'])
            pixmap = QPixmap.fromImage(ir_edges_qimg)
            scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio)
            self.ir_edges_label.setPixmap(scaled_pixmap)
        
        # 结构特征
        if 'structure' in visible_features:
            # 将拉普拉斯结果转换为可显示的范围
            visible_structure = np.uint8(np.absolute(visible_features['structure']))
            visible_structure_qimg = convert_to_qimage(visible_structure)
            pixmap = QPixmap.fromImage(visible_structure_qimg)
            scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio)
            self.visible_structure_label.setPixmap(scaled_pixmap)
        
        if 'structure' in ir_features:
            # 将拉普拉斯结果转换为可显示的范围
            ir_structure = np.uint8(np.absolute(ir_features['structure']))
            ir_structure_qimg = convert_to_qimage(ir_structure)
            pixmap = QPixmap.fromImage(ir_structure_qimg)
            scaled_pixmap = pixmap.scaled(300, 200, Qt.KeepAspectRatio)
            self.ir_structure_label.setPixmap(scaled_pixmap)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
        # 初始化变量
        self.visible_path = ""
        self.ir_path = ""
        self.is_video = False
        self.video_thread = None
        self.processing_thread = None
        self.feature_data = []
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("多光谱图像特征分析与权重计算系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部按钮布局
        button_layout = QHBoxLayout()
        
        # 选择可见光图像/视频按钮
        self.select_visible_btn = QPushButton("选择可见光图像/视频")
        self.select_visible_btn.clicked.connect(self.select_visible_file)
        button_layout.addWidget(self.select_visible_btn)
        
        # 选择红外图像/视频按钮
        self.select_ir_btn = QPushButton("选择红外图像/视频")
        self.select_ir_btn.clicked.connect(self.select_ir_file)
        button_layout.addWidget(self.select_ir_btn)
        
        # 处理按钮
        self.process_btn = QPushButton("处理")
        self.process_btn.clicked.connect(self.process_files)
        button_layout.addWidget(self.process_btn)
        
        # 保存数据按钮
        self.save_btn = QPushButton("保存数据")
        self.save_btn.clicked.connect(self.save_data)
        self.save_btn.setEnabled(False)
        button_layout.addWidget(self.save_btn)
        
        main_layout.addLayout(button_layout)
        
        # 创建标签显示所选文件
        file_info_layout = QHBoxLayout()
        self.visible_file_label = QLabel("可见光文件: 未选择")
        self.ir_file_label = QLabel("红外文件: 未选择")
        file_info_layout.addWidget(self.visible_file_label)
        file_info_layout.addWidget(self.ir_file_label)
        main_layout.addLayout(file_info_layout)
        
        # 创建标签显示处理状态
        self.status_label = QLabel("状态: 就绪")
        main_layout.addWidget(self.status_label)
        
        # 创建标签页
        self.tabs = QTabWidget()
        
        # 特征可视化标签页
        self.feature_tab = FeatureVisualizer()
        self.tabs.addTab(self.feature_tab, "特征可视化")
        
        # 特征差异标签页
        self.difference_tab = FeatureDifferenceVisualizer()
        self.tabs.addTab(self.difference_tab, "特征差异")
        
        main_layout.addWidget(self.tabs)
    
    def select_visible_file(self):
        """选择可见光图像或视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择可见光图像/视频", "", 
            "图像文件 (*.png *.jpg *.jpeg *.bmp);;视频文件 (*.mp4 *.avi *.mov);;所有文件 (*)"
        )
        
        if file_path:
            self.visible_path = file_path
            self.visible_file_label.setText(f"可见光文件: {os.path.basename(file_path)}")
            self.update_process_button_state()
    
    def select_ir_file(self):
        """选择红外图像或视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择红外图像/视频", "", 
            "图像文件 (*.png *.jpg *.jpeg *.bmp);;视频文件 (*.mp4 *.avi *.mov);;所有文件 (*)"
        )
        
        if file_path:
            self.ir_path = file_path
            self.ir_file_label.setText(f"红外文件: {os.path.basename(file_path)}")
            self.update_process_button_state()
    
    def update_process_button_state(self):
        """更新处理按钮状态"""
        if self.visible_path and self.ir_path:
            self.process_btn.setEnabled(True)
        else:
            self.process_btn.setEnabled(False)
    
    def process_files(self):
        """处理所选文件"""
        self.process_btn.setEnabled(False)
        self.save_btn.setEnabled(False)
        self.status_label.setText("状态: 处理中...")
        
        # 重置特征数据列表
        self.feature_data = []
        
        # 判断是图像还是视频
        visible_ext = os.path.splitext(self.visible_path)[1].lower()
        ir_ext = os.path.splitext(self.ir_path)[1].lower()
        
        self.is_video = (visible_ext in ['.mp4', '.avi', '.mov']) and (ir_ext in ['.mp4', '.avi', '.mov'])
        
        if self.is_video:
            # 处理视频
            self.process_video()
        else:
            # 处理图像
            self.process_image()
    
    def process_image(self):
        """处理图像"""
        # 读取图像
        visible_image = cv2.imread(self.visible_path)
        ir_image = cv2.imread(self.ir_path)
        
        # 调整红外图像大小以匹配可见光图像
        if visible_image.shape[:2] != ir_image.shape[:2]:
            ir_image = cv2.resize(ir_image, (visible_image.shape[1], visible_image.shape[0]))
        
        # 在单独的线程中处理图像
        self.processing_thread = ImageProcessorThread(visible_image, ir_image)
        self.processing_thread.update_signal.connect(self.update_results)
        self.processing_thread.start()
    
    def process_video(self):
        """处理视频"""
        # 创建视频处理线程
        self.video_thread = VideoThread(self.visible_path, self.ir_path)
        self.video_thread.update_signal.connect(self.update_video_results)
        self.video_thread.start()
    
    def update_results(self, data):
        """更新处理结果"""
        if 'error' in data:
            self.status_label.setText(f"状态: 错误 - {data['error']}")
            QMessageBox.critical(self, "错误", data['error'])
        else:
            # 更新特征可视化
            self.feature_tab.update_feature_chart(data['visible_features'], data['ir_features'])
            self.feature_tab.update_weight_pie(data['weights']['visible_weight'], data['weights']['ir_weight'])
            self.feature_tab.update_scene_label(data['weights']['is_day'])
            
            # 更新特征差异
            self.difference_tab.update_feature_differences(data['visible_features'], data['ir_features'])
            self.difference_tab.update_feature_images(
                cv2.imread(self.visible_path), 
                cv2.imread(self.ir_path),
                data['visible_features'],
                data['ir_features']
            )
            
            # 保存特征数据
            self.save_feature_data(data)
            
            self.status_label.setText("状态: 处理完成")
            self.save_btn.setEnabled(True)
        
        self.process_btn.setEnabled(True)
        self.processing_thread = None
    
    def update_video_results(self, data):
        """更新视频处理结果"""
        if 'error' in data:
            self.status_label.setText(f"状态: 错误 - {data['error']}")
            QMessageBox.critical(self, "错误", data['error'])
            self.video_thread = None
            self.process_btn.setEnabled(True)
        elif 'finished' in data:
            self.status_label.setText("状态: 视频处理完成")
            self.video_thread = None
            self.process_btn.setEnabled(True)
            self.save_btn.setEnabled(True)
        else:
            # 更新特征可视化
            self.feature_tab.update_feature_chart(data['visible_features'], data['ir_features'])
            self.feature_tab.update_weight_pie(data['weights']['visible_weight'], data['weights']['ir_weight'])
            self.feature_tab.update_scene_label(data['weights']['is_day'])
            
            # 更新特征差异
            self.difference_tab.update_feature_differences(data['visible_features'], data['ir_features'])
            self.difference_tab.update_feature_images(
                data['visible_frame'], 
                data['ir_frame'],
                data['visible_features'],
                data['ir_features']
            )
            
            # 保存特征数据
            self.save_feature_data(data)
    
    def save_feature_data(self, data):
        """保存特征数据"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        feature_data = {
            'timestamp': timestamp,
            'visible_edge_density': data['visible_features']['edge_density'],
            'visible_structure_complexity': data['visible_features']['structure_complexity'],
            'visible_entropy': data['visible_features']['entropy'],
            'visible_contrast': data['visible_features']['contrast'],
            'visible_brightness': data['visible_features']['brightness'],
            'ir_edge_density': data['ir_features']['edge_density'],
            'ir_structure_complexity': data['ir_features']['structure_complexity'],
            'ir_entropy': data['ir_features']['entropy'],
            'ir_contrast': data['ir_features']['contrast'],
            'ir_brightness': data['ir_features']['brightness'],
            'visible_weight': data['weights']['visible_weight'],
            'ir_weight': data['weights']['ir_weight'],
            'is_day': data['weights']['is_day']
        }
        
        self.feature_data.append(feature_data)
    
    def save_data(self):
        """保存数据到CSV文件"""
        if not self.feature_data:
            QMessageBox.warning(self, "警告", "没有数据可保存")
            return
        
        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存数据", "", "CSV文件 (*.csv);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 创建DataFrame
                df = pd.DataFrame(self.feature_data)
                
                # 保存到CSV
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                
                QMessageBox.information(self, "成功", f"数据已成功保存到 {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存数据时出错: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())    