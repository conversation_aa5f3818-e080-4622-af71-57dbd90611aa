from ultralytics import YOLO
import cv2
import os


class YOLODetector:
    def __init__(self, model_path):
        """
        初始化 YOLO 模型
        :param model_path: 训练好的模型权重路径
        """
        self.model = YOLO(model_path)

    def set_input_output_paths(self, input_folder, output_folder):
        """
        设置输入输出路径
        :param input_folder: 输入图像文件夹路径
        :param output_folder: 输出图像文件夹路径
        """
        self.input_folder = input_folder
        self.output_folder = output_folder
        os.makedirs(self.output_folder, exist_ok=True)

    def detect_and_draw_boxes(self, conf_threshold=0.25):
        """
        遍历图像并进行目标检测，绘制边界框并保存结果
        :param conf_threshold: 置信度阈值
        """
        all_results = []
        for img_file in os.listdir(self.input_folder):
            if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                img_path = os.path.join(self.input_folder, img_file)
                results = self.model.predict(source=img_path, save=False, conf=conf_threshold)

                # 读取原始图像用于绘制
                img = cv2.imread(img_path)

                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            xyxy = box.xyxy[0].tolist()  # [x1, y1, x2, y2]
                            confidence = box.conf[0].item()
                            cls = box.cls[0].item()
                            all_results.append({
                                'image_path': img_path,
                                'box': xyxy,
                                'confidence': confidence,
                                'class': cls
                            })

                            # 修改置信度（示例：增加0.1，不超过1.0）
                            modified_confidence = min(confidence + 0.1, 1.0)

                            # 打印信息
                            print(f"检测框: {xyxy}, 原置信度: {confidence:.2f}, 修改后置信度: {modified_confidence:.2f}, 类别: {cls}")

                            # 绘制矩形框
                            x1, y1, x2, y2 = map(int, xyxy)
                            cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 2)  # 红色框

                            # 显示修改后的置信度和类别
                            label = f"{self.model.names[int(cls)]} {modified_confidence:.2f}"
                            cv2.putText(img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                # 保存带有新框和置信度的图像
                output_path = os.path.join(self.output_folder, img_file)
                cv2.imwrite(output_path, img)
                print(f"Saved result to {output_path}")
        return all_results


class Redraw:
    def __init__(self):
        """
        初始化 YOLO 模型
        :param model_path: 训练好的模型权重路径
        """
        super().__init__()
    def draw_rectangle_on_image(image_path, points, output_path, color=(0, 0, 255), thickness=2):
        """
        在指定图像上根据四个点绘制矩形框
        :param image_path: 输入图像路径
        :param points: 四个点的坐标列表 [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        :param output_path: 输出图像路径（如果为 None，则覆盖原图）
        :param color: 矩形框颜色（BGR 格式）
        :param thickness: 线条粗细
        :return: None
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise FileNotFoundError(f"无法加载图像: {image_path}")

        # 提取所有 x 和 y 坐标
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]

        # 找到左上角和右下角
        top_left = (min(x_coords), min(y_coords))
        bottom_right = (max(x_coords), max(y_coords))

        # 绘制矩形
        cv2.rectangle(image, top_left, bottom_right, color, thickness)

        # 保存图像
        if output_path is None:
            output_path = image_path
        cv2.imwrite(output_path, image)
        print(f"已保存带有矩形框的图像到: {output_path}")

if __name__ == "__main__":
    # 模型路径
    model_path = "yolov11n.pt"

    # 创建检测器实例
    detector = YOLODetector(model_path)

    # 设置输入输出路径
    input_folder = r'E:/workspace/test_project/visible'
    output_folder = r'E:/workspace/test_project/results'
    detector.set_input_output_paths(input_folder, output_folder)

    # 开始检测
    a = detector.detect_and_draw_boxes(conf_threshold=0.25)
    print(a)



    # #image_path =r"E:\z30\夏天拍照\导出\DSC_0135-23.jpg"
    # points = [(100, 100), (150, 100), (100, 200), (150, 200)]  # 四个点坐标
    # output_path = "E:\z30\夏天拍照\导出\drawn_example.jpg"
    # Redraw.draw_rectangle_on_image(image_path, points, output_path)
