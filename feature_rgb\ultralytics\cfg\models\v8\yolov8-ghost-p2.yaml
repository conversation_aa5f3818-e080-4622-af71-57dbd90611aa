# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Ultralytics YOLOv8 object detection model with P2/4 - P5/32 outputs
# Model docs: https://docs.ultralytics.com/models/yolov8
# Task docs: https://docs.ultralytics.com/tasks/detect
# Employs Ghost convolutions and modules proposed in Huawei's GhostNet in https://arxiv.org/abs/1911.11907v2

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024] # YOLOv8n-ghost-p2 summary: 290 layers, 2033944 parameters, 2033928 gradients, 13.8 GFLOPs
  s: [0.33, 0.50, 1024] # YOLOv8s-ghost-p2 summary: 290 layers, 5562080 parameters, 5562064 gradients, 25.1 GFLOPs
  m: [0.67, 0.75, 768] # YOLOv8m-ghost-p2 summary: 434 layers, 9031728 parameters, 9031712 gradients, 42.8 GFLOPs
  l: [1.00, 1.00, 512] # YOLOv8l-ghost-p2 summary: 578 layers, 12214448 parameters, 12214432 gradients, 69.1 GFLOPs
  x: [1.00, 1.25, 512] # YOLOv8x-ghost-p2 summary: 578 layers, 18664776 parameters, 18664760 gradients, 103.3 GFLOPs

# YOLOv8.0-ghost backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, GhostConv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C3Ghost, [128, True]]
  - [-1, 1, GhostConv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C3Ghost, [256, True]]
  - [-1, 1, GhostConv, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C3Ghost, [512, True]]
  - [-1, 1, GhostConv, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C3Ghost, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]] # 9

# YOLOv8.0-ghost-p2 head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 6], 1, Concat, [1]] # cat backbone P4
  - [-1, 3, C3Ghost, [512]] # 12

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 4], 1, Concat, [1]] # cat backbone P3
  - [-1, 3, C3Ghost, [256]] # 15 (P3/8-small)

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 2], 1, Concat, [1]] # cat backbone P2
  - [-1, 3, C3Ghost, [128]] # 18 (P2/4-xsmall)

  - [-1, 1, GhostConv, [128, 3, 2]]
  - [[-1, 15], 1, Concat, [1]] # cat head P3
  - [-1, 3, C3Ghost, [256]] # 21 (P3/8-small)

  - [-1, 1, GhostConv, [256, 3, 2]]
  - [[-1, 12], 1, Concat, [1]] # cat head P4
  - [-1, 3, C3Ghost, [512]] # 24 (P4/16-medium)

  - [-1, 1, GhostConv, [512, 3, 2]]
  - [[-1, 9], 1, Concat, [1]] # cat head P5
  - [-1, 3, C3Ghost, [1024]] # 27 (P5/32-large)

  - [[18, 21, 24, 27], 1, Detect, [nc]] # Detect(P2, P3, P4, P5)
