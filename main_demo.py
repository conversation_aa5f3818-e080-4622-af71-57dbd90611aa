"""
图像分析系统主程序演示
"""
import cv2
import numpy as np
import matplotlib.pyplot as plt
from image_analysis_system import ImageAnalysisSystem
import os
#显示中文
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文

def main():
    """主程序入口"""
    print("=== 图像分析系统演示 ===")
    
    # 初始化系统
    system = ImageAnalysisSystem()
    
    # 示例参数
    image_path = "000000.jpg"  # 使用项目中的示例图像
    scene_description = "晴天"
    
    # 示例边界框（需要根据实际图像调整）
    individual_bbox = (100, 100, 200, 150)  # (x, y, width, height)
    component_bbox = (150, 125, 80, 60)
    
    entity_type = "电科一号"
    component_name = "雷达"
    
    try:
        # 检查图像是否存在
        if not os.path.exists(image_path):
            print(f"图像文件不存在: {image_path}")
            # 创建一个示例图像用于演示
            demo_image = create_demo_image()
            cv2.imwrite("demo_image.jpg", demo_image)
            image_path = "demo_image.jpg"
            print("已创建演示图像: demo_image.jpg")
        
        # 执行完整的图像处理流程
        print(f"处理图像: {image_path}")
        results = system.process_image(
            image_path=image_path,
            scene_description=scene_description,
            individual_bbox=individual_bbox,
            component_bbox=component_bbox,
            entity_type=entity_type,
            component_name=component_name
        )
        
        # 显示结果
        print("\n=== 处理结果 ===")
        
        # 1. 图像特征
        print("\n1. 图像特征:")
        for feature, value in results['image_features'].items():
            print(f"   {feature}: {value:.4f}")
        
        # 2. 场景对比结果
        print(f"\n2. 场景对比:")
        scene_info = results['scene_comparison']
        print(f"   场景描述: {scene_info['scene_description']}")
        print(f"   匹配度: {scene_info['match_score']:.4f}")
        print(f"   是否匹配: {'是' if scene_info['is_match'] else '否'}")
        
        # 3. 几何特征
        print(f"\n3. 几何特征:")
        geo_features = results['geometric_features']
        print(f"   长宽比: {geo_features['aspect_ratio']:.4f}")
        print(f"   圆度: {geo_features['circularity']:.4f}")
        print(f"   高热区域位置: {geo_features['hot_region_position']}")
        print(f"   背景对比度: {geo_features['background_contrast']:.2f}")
        
        # 4. 部件关系查询
        print(f"\n4. 部件关系查询:")
        comp_relations = results['component_relations']
        print(f"   部件名称: {comp_relations['component_name']}")
        print(f"   个体类型: {comp_relations['entity_type']}")
        print(f"   属于该个体: {'是' if comp_relations['belongs_to_entity'] else '否'}")
        
        if comp_relations['related_components']:
            print("   相关部件:")
            for comp in comp_relations['related_components']:
                print(f"     - {comp['component']}: {comp['relation']} ({comp['position']})")
        
        # 5. 相关边界框
        print(f"\n5. 生成的相关边界框:")
        if results['related_bboxes']:
            for bbox_info in results['related_bboxes']:
                print(f"   {bbox_info['component']}: {bbox_info['bbox']} ({bbox_info['position']})")
        else:
            print("   无相关边界框")
        
        # 6. 保存和显示可视化结果
        result_image = results['result_image']
        output_path = "analysis_result.jpg"
        cv2.imwrite(output_path, result_image)
        print(f"\n6. 可视化结果已保存到: {output_path}")
        
        # 使用matplotlib显示结果
        display_results(image_path, result_image, results)
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def create_demo_image():
    """创建演示图像"""
    # 创建一个640x480的彩色图像
    image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加背景渐变
    for i in range(480):
        for j in range(640):
            image[i, j] = [100 + i//4, 150 + j//8, 200 - i//6]
    
    # 添加一些几何形状作为目标
    # 个体区域（矩形）
    cv2.rectangle(image, (100, 100), (300, 250), (0, 255, 0), 2)
    cv2.fillPoly(image, [np.array([(120, 120), (280, 120), (280, 230), (120, 230)])], (80, 120, 160))
    
    # 部件区域（圆形）
    cv2.circle(image, (200, 175), 30, (255, 0, 0), 2)
    cv2.circle(image, (200, 175), 25, (200, 100, 100), -1)
    
    # 添加一些噪声
    noise = np.random.randint(0, 50, image.shape, dtype=np.uint8)
    image = cv2.add(image, noise)
    
    return image

def display_results(original_path, result_image, results):
    """显示分析结果"""
    try:
        # 读取原始图像
        original_image = cv2.imread(original_path)
        if original_image is not None:
            original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        
        result_image_rgb = cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB)
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('图像分析系统结果', fontsize=16)
        
        # 原始图像
        if original_image is not None:
            axes[0, 0].imshow(original_image)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')
        
        # 分析结果图像
        axes[0, 1].imshow(result_image_rgb)
        axes[0, 1].set_title('分析结果')
        axes[0, 1].axis('off')
        
        # 特征对比图
        features = results['image_features']
        feature_names = list(features.keys())
        feature_values = list(features.values())
        
        axes[1, 0].bar(range(len(feature_names)), feature_values)
        axes[1, 0].set_xticks(range(len(feature_names)))
        axes[1, 0].set_xticklabels(feature_names, rotation=45, ha='right')
        axes[1, 0].set_title('图像特征')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 几何特征饼图
        geo_features = results['geometric_features']
        geo_names = ['长宽比', '圆度', '背景对比度']
        geo_values = [
            geo_features['aspect_ratio'],
            geo_features['circularity'],
            geo_features['background_contrast'] / 100  # 归一化
        ]
        
        axes[1, 1].pie(geo_values, labels=geo_names, autopct='%1.2f', startangle=90)
        axes[1, 1].set_title('几何特征分布')
        
        plt.tight_layout()
        plt.savefig('analysis_visualization.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("可视化图表已保存到: analysis_visualization.png")
        
    except Exception as e:
        print(f"显示结果时出现错误: {e}")

def test_individual_functions():
    """测试各个功能模块"""
    print("\n=== 功能模块测试 ===")
    
    system = ImageAnalysisSystem()
    
    # 创建测试图像
    test_image = create_demo_image()
    
    # 测试特征提取
    print("\n1. 测试特征提取:")
    features = system.extract_image_features(test_image)
    for name, value in features.items():
        print(f"   {name}: {value:.4f}")
    
    # 测试场景对比
    print("\n2. 测试场景对比:")
    scene_result = system.compare_with_scene(features, "晴天")
    print(f"   匹配度: {scene_result['match_score']:.4f}")
    print(f"   是否匹配: {scene_result['is_match']}")
    
    # 测试几何特征提取
    print("\n3. 测试几何特征提取:")
    bbox = (100, 100, 200, 150)
    geo_features = system.extract_geometric_features(test_image, bbox)
    for name, value in geo_features.items():
        print(f"   {name}: {value}")
    
    # 测试部件查询
    print("\n4. 测试部件查询:")
    comp_result = system.query_component_relations("雷达", "电科一号")
    print(f"   属于个体: {comp_result['belongs_to_entity']}")
    print(f"   相关部件数量: {len(comp_result['related_components'])}")

if __name__ == "__main__":
    # 运行主程序
    main()
    
    # 运行功能测试
    test_individual_functions()
