# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Ultralytics YOLO11 object detection model with P3/8 - P5/32 outputs
# Model docs: https://docs.ultralytics.com/models/yolo11
# Task docs: https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolo11n.yaml' will call yolo11.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.25, 1024] # summary: 181 layers, 2624080 parameters, 2624064 gradients, 6.6 GFLOPs
  s: [0.50, 0.50, 1024] # summary: 181 layers, 9458752 parameters, 9458736 gradients, 21.7 GFLOPs
  m: [0.50, 1.00, 512] # summary: 231 layers, 20114688 parameters, 20114672 gradients, 68.5 GFLOPs
  l: [1.00, 1.00, 512] # summary: 357 layers, 25372160 parameters, 25372144 gradients, 87.6 GFLOPs
  x: [1.00, 1.50, 512] # summary: 357 layers, 56966176 parameters, 56966160 gradients, 196.0 GFLOPs

# YOLO11n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 2, Conv, [64, 3, 2]] # 0-P1/2
  #此处添加卷积后的特征提取层  TensorFeatureExtractor
  - [-1, 2, TensorFeatureExtractor, [256]] #1
  - [-1, 2, C3k2, [256, False, 0.25]] #2
  - [-1, 2, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 2, C3k2, [512, False]] # 4-P4/16
  - [-1, 2, Conv, [1024, 3, 2]] # 5
  - [-1, 1, SPPF, [1024, 5]] # 6
  - [-1, 2, C2PSA, [1024]] # 7

# YOLO11n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] #8
  - [-1, 2, C3k2, [512, False]] # 9

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] #10
  - [-1, 2, C3k2, [256, False]] # 11 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]] #12
  - [-1, 2, C3k2, [512, False]] # 13 (P4/16-medium)

  - [-1, 1, Conv, [512, 3, 2]] #14

  - [-1, 2, C3k2, [1024, True]] # 15 (P5/32-large)

  - [[9, 11, 13], 1, Detect, [nc]] #26  Detect(P3, P4, P5)
