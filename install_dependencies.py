#!/usr/bin/env python3
"""
安装RDF处理所需的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ 安装 {package} 失败")
        return False

def main():
    print("正在安装RDF处理所需的依赖包...")
    
    packages = [
        "rdflib",  # RDF处理核心库
        "rdflib-jsonld",  # JSON-LD支持
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("\n所有依赖已成功安装！现在可以运行 read_rdf.py 来读取RDF文件了。")
    else:
        print("\n部分依赖安装失败，请手动安装缺失的包。")

if __name__ == "__main__":
    main()
