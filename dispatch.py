import cv2
import numpy as np
from skimage import filters
from skimage.util import img_as_ubyte
from scipy.stats import entropy

def compute_entropy(image):
    """计算图像信息熵"""
    hist, _ = np.histogram(image.flatten(), bins=256, range=[0,256], density=True)
    return entropy(hist + 1e-7)  # 加上小值防止log(0)

def compute_contrast(image):
    """局部对比度估计"""
    return image.std()

def compute_edge_strength(image):
    """Sobel边缘强度"""
    sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
    return np.mean(np.sqrt(sobelx**2 + sobely**2))

def compute_structure(image):
    """Laplacian结构复杂度"""
    laplacian = cv2.Laplacian(image, cv2.CV_64F)
    return np.var(laplacian)

def compute_gray_mean(image):
    """灰度均值"""
    return np.mean(image)

def extract_features(image):
    """统一特征提取接口"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
    features = {
        'entropy': compute_entropy(gray),
        'contrast': compute_contrast(gray),
        'edge': compute_edge_strength(gray),
        'structure': compute_structure(gray),
        'gray_mean': compute_gray_mean(gray)
    }
    return features

def normalize_features(feat_dict):
    """特征归一化并合成一个加权指标（可以改进为学习得到）"""
    feat_values = np.array(list(feat_dict.values()))
    norm = (feat_values - feat_values.min()) / (feat_values.ptp() + 1e-6)
    return np.mean(norm)

def decide_strategy(vis_weight, ir_weight, threshold=0.2):
    """根据权重差异决定使用单模或融合模型"""
    diff = abs(vis_weight - ir_weight)
    if diff > threshold:
        if vis_weight > ir_weight:
            return 'visible'
        else:
            return 'infrared'
    else:
        return 'fusion'

def run_strategy(vis_frame, ir_frame):
    vis_feat = extract_features(vis_frame)
    ir_feat = extract_features(ir_frame)

    vis_weight = normalize_features(vis_feat)
    ir_weight = normalize_features(ir_feat)

    decision = decide_strategy(vis_weight, ir_weight)

    print(f"[INFO] 可见光权重: {vis_weight:.3f} | 红外权重: {ir_weight:.3f} => 策略: {decision}")
    
    # 模拟调用对应识别模型
    if decision == 'visible':
        print("调用可见光模型识别...")
    elif decision == 'infrared':
        print("调用红外模型识别...")
    else:
        print("调用融合模型识别...")

    return decision, vis_weight, ir_weight

if __name__ == "__main__":
    vis_cap = cv2.VideoCapture("T_video/0016_T.mp4")
    ir_cap = cv2.VideoCapture("V_video/0016_V.mp4")

    while vis_cap.isOpened() and ir_cap.isOpened():
        ret1, vis_frame = vis_cap.read()
        ret2, ir_frame = ir_cap.read()
        if not ret1 or not ret2:
            break

        decision, vis_w, ir_w = run_strategy(vis_frame, ir_frame)

        cv2.imshow("Visible", vis_frame)
        cv2.imshow("Infrared", ir_frame)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    vis_cap.release()
    ir_cap.release()
    cv2.destroyAllWindows()


