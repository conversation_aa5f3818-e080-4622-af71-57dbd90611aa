import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage import measure, morphology, color
from scipy import ndimage
import math

def extract_and_visualize_geometric_features(binary_image, title="几何特征可视化"):
    """
    从二值图像中提取目标几何特征并可视化结果
    
    参数:
    binary_image: 二值图像 (背景为0, 目标为255)
    title: 可视化标题
    
    返回:
    包含各种几何特征的字典
    """
    # 创建可视化画布
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    fig.suptitle(title, fontsize=16)
    
    # 准备彩色图像用于可视化
    color_image = cv2.cvtColor(binary_image, cv2.COLOR_GRAY2BGR)
    color_image_clean = color_image.copy()
    
    features = {}
    contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    if not contours:
        plt.close(fig)
        return features
    
    # 取最大轮廓
    contour = max(contours, key=cv2.contourArea)
    
    # 1. 基本特征
    features['area'] = cv2.contourArea(contour)
    features['perimeter'] = cv2.arcLength(contour, True)
    
    # 2. 圆形相关特征
    if features['perimeter'] > 0:
        features['circularity'] = (4 * np.pi * features['area']) / (features['perimeter'] ** 2)
    
    features['equivalent_diameter'] = np.sqrt(4 * features['area'] / np.pi)
    
    # 最小外接圆
    (x, y), radius = cv2.minEnclosingCircle(contour)
    center = (int(x), int(y))
    features['min_enclosing_circle_diameter'] = 2 * radius
    features['min_enclosing_circle_area'] = np.pi * (radius ** 2)
    features['circularity_ratio'] = features['area'] / features['min_enclosing_circle_area'] if features['min_enclosing_circle_area'] > 0 else 0
    
    # 3. 矩形相关特征
    rect = cv2.minAreaRect(contour)
    box = cv2.boxPoints(rect)
    box = np.int0(box)
    width, height = rect[1]
    features['bounding_rect_width'] = min(width, height)
    features['bounding_rect_height'] = max(width, height)
    features['aspect_ratio'] = features['bounding_rect_height'] / features['bounding_rect_width'] if features['bounding_rect_width'] > 0 else 0
    
    bounding_rect_area = width * height
    features['rectangularity'] = features['area'] / bounding_rect_area if bounding_rect_area > 0 else 0
    
    # 4. 凸性特征
    hull = cv2.convexHull(contour)
    features['convex_area'] = cv2.contourArea(hull)
    features['convexity'] = features['area'] / features['convex_area'] if features['convex_area'] > 0 else 0
    
    convex_perimeter = cv2.arcLength(hull, True)
    features['convex_perimeter_ratio'] = features['perimeter'] / convex_perimeter if convex_perimeter > 0 else 0
    
    # 5. 椭圆拟合特征
    if len(contour) >= 5:
        ellipse = cv2.fitEllipse(contour)
        (center_ell, axes, orientation) = ellipse
        major_axis = max(axes)
        minor_axis = min(axes)
        features['major_axis_length'] = major_axis
        features['minor_axis_length'] = minor_axis
        
        if minor_axis > 0:
            features['eccentricity'] = np.sqrt(1 - (minor_axis ** 2) / (major_axis ** 2))
            features['axis_ratio'] = major_axis / minor_axis
    else:
        ellipse = None
    
    # 6. 紧密度
    if features['area'] > 0:
        features['compactness'] = (features['perimeter'] ** 2) / (4 * np.pi * features['area'])
    
    # 7. 多边形特征
    epsilon = 0.015 * cv2.arcLength(contour, True)
    approx = cv2.approxPolyDP(contour, epsilon, True)
    features['polygon_sides'] = len(approx)
    
    # 8. 矩特征
    M = cv2.moments(contour)
    if M['m00'] > 0:
        cx = int(M['m10'] / M['m00'])
        cy = int(M['m01'] / M['m00'])
        features['centroid'] = (cx, cy)
        
        hu_moments = cv2.HuMoments(M)
        for i in range(7):
            features[f'hu_moment_{i+1}'] = -np.sign(hu_moments[i]) * np.log10(np.abs(hu_moments[i]))
        
        # 主轴方向
        if M['mu20'] - M['mu02'] != 0:
            orientation = 0.5 * np.arctan2(2 * M['mu11'], M['mu20'] - M['mu02'])
            features['orientation'] = orientation
            # 计算主轴端点
            length = 100
            x1 = cx + length * np.cos(orientation)
            y1 = cy + length * np.sin(orientation)
            x2 = cx - length * np.cos(orientation)
            y2 = cy - length * np.sin(orientation)
            major_axis_pts = [(int(x1), int(y1)), (int(x2), int(y2))]
            features['major_axis_pts'] = major_axis_pts
    
    # 9. 拓扑特征
    inverted = np.where(binary_image == 255, 0, 1).astype(np.uint8)
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(inverted, 8, cv2.CV_32S)
    features['num_holes'] = num_labels - 1
    
    # 10. 骨架特征
    skeleton = morphology.skeletonize(binary_image // 255)
    skeleton_points = np.argwhere(skeleton > 0)
    features['skeleton_length'] = len(skeleton_points)
    
    skeleton_uint8 = skeleton.astype(np.uint8) * 255
    skeleton_contours, _ = cv2.findContours(skeleton_uint8, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    
    branch_points = 0
    end_points = 0
    
    for point in skeleton_points:
        y, x = point
        if y > 0 and x > 0 and y < skeleton.shape[0]-1 and x < skeleton.shape[1]-1:
            neighborhood = skeleton_uint8[y-1:y+2, x-1:x+2]
            if neighborhood.size == 9:
                neighbors = np.count_nonzero(neighborhood)
                if neighbors == 2:
                    end_points += 1
                elif neighbors >= 4:
                    branch_points += 1
    
    features['skeleton_endpoints'] = end_points
    features['skeleton_branchpoints'] = branch_points
    
    # 11. Feret直径
    angles = np.arange(0, 180, 15)
    feret_diameters = []
    feret_lines = []
    
    if M['m00'] > 0:
        cx, cy = features['centroid']
        for angle in angles:
            rot_matrix = cv2.getRotationMatrix2D((cx, cy), angle, 1.0)
            rotated_img = cv2.warpAffine(binary_image, rot_matrix, binary_image.shape[1::-1], 
                                        flags=cv2.INTER_LINEAR, borderValue=0)
            
            coords = np.column_stack(np.where(rotated_img > 0))
            if len(coords) > 0:
                min_x = np.min(coords[:, 1])
                max_x = np.max(coords[:, 1])
                feret_diameters.append(max_x - min_x)
                
                # 计算原始坐标系中的点
                angle_rad = np.deg2rad(angle)
                rot_matrix_inv = cv2.getRotationMatrix2D((cx, cy), -angle, 1.0)
                pt1 = np.dot(rot_matrix_inv, [min_x, cy, 1])
                pt2 = np.dot(rot_matrix_inv, [max_x, cy, 1])
                feret_lines.append((pt1[:2], pt2[:2]))
    
    if feret_diameters:
        features['min_feret_diameter'] = min(feret_diameters)
        features['max_feret_diameter'] = max(feret_diameters)
        features['feret_ratio'] = features['max_feret_diameter'] / features['min_feret_diameter'] if features['min_feret_diameter'] > 0 else 0
    
    # =========================================
    # 可视化部分
    # =========================================
    
    # 子图1: 原始轮廓和基本特征
    img1 = color_image.copy()
    cv2.drawContours(img1, [contour], -1, (0, 255, 0), 2)
    cv2.putText(img1, f"Area: {features['area']:.0f}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    cv2.putText(img1, f"Perimeter: {features['perimeter']:.1f}", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    cv2.putText(img1, f"Circularity: {features.get('circularity', 0):.3f}", (10, 90), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    axes[0, 0].imshow(cv2.cvtColor(img1, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title("轮廓和基本特征")
    axes[0, 0].axis('off')
    
    # 子图2: 包围形状
    img2 = color_image.copy()
    # 绘制最小外接圆
    cv2.circle(img2, center, int(radius), (255, 0, 0), 2)
    # 绘制最小外接矩形
    cv2.drawContours(img2, [box], 0, (0, 0, 255), 2)
    # 绘制凸包
    cv2.drawContours(img2, [hull], -1, (0, 255, 255), 2)
    
    # 添加文本
    cv2.putText(img2, f"Min Enclosing Circle", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
    cv2.putText(img2, f"Min Area Rect", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    cv2.putText(img2, f"Convex Hull", (10, 90), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    
    axes[0, 1].imshow(cv2.cvtColor(img2, cv2.COLOR_BGR2RGB))
    axes[0, 1].set_title("包围形状")
    axes[0, 1].axis('off')
    
    # 子图3: 椭圆和主轴
    img3 = color_image.copy()
    if ellipse is not None:
        cv2.ellipse(img3, ellipse, (255, 0, 255), 2)
    
    # 绘制主轴
    if 'major_axis_pts' in features:
        pt1, pt2 = features['major_axis_pts']
        cv2.line(img3, pt1, pt2, (0, 255, 255), 2)
    
    # 绘制质心
    if 'centroid' in features:
        cx, cy = features['centroid']
        cv2.circle(img3, (cx, cy), 5, (0, 0, 255), -1)
    
    # 添加文本
    if ellipse is not None:
        cv2.putText(img3, f"Major Axis: {features.get('major_axis_length', 0):.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
        cv2.putText(img3, f"Minor Axis: {features.get('minor_axis_length', 0):.1f}", (10, 60), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
        cv2.putText(img3, f"Eccentricity: {features.get('eccentricity', 0):.3f}", (10, 90), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
    
    axes[0, 2].imshow(cv2.cvtColor(img3, cv2.COLOR_BGR2RGB))
    axes[0, 2].set_title("椭圆拟合和主轴")
    axes[0, 2].axis('off')
    
    # 子图4: 骨架
    img4 = color_image.copy()
    # 绘制骨架
    skeleton_color = np.zeros_like(img4)
    skeleton_color[skeleton] = [0, 0, 255]  # 红色骨架
    img4 = cv2.addWeighted(img4, 0.7, skeleton_color, 0.3, 0)
    
    # 绘制分支点和端点
    for point in skeleton_points:
        y, x = point
        if y > 0 and x > 0 and y < skeleton.shape[0]-1 and x < skeleton.shape[1]-1:
            neighborhood = skeleton_uint8[y-1:y+2, x-1:x+2]
            if neighborhood.size == 9:
                neighbors = np.count_nonzero(neighborhood)
                if neighbors == 2:  # 端点
                    cv2.circle(img4, (x, y), 3, (0, 255, 0), -1)  # 绿色端点
                elif neighbors >= 4:  # 分支点
                    cv2.circle(img4, (x, y), 5, (255, 0, 0), -1)  # 蓝色分支点
    
    # 添加文本
    cv2.putText(img4, f"Endpoints: {features['skeleton_endpoints']}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    cv2.putText(img4, f"Branchpoints: {features['skeleton_branchpoints']}", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
    cv2.putText(img4, f"Length: {features['skeleton_length']}", (10, 90), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    
    axes[0, 3].imshow(cv2.cvtColor(img4, cv2.COLOR_BGR2RGB))
    axes[0, 3].set_title("骨架分析")
    axes[0, 3].axis('off')
    
    # 子图5: Feret直径
    img5 = color_image.copy()
    if feret_lines:
        # 绘制所有Feret线
        for i, (pt1, pt2) in enumerate(feret_lines):
            color = (int(i*15), 255-int(i*15), 100)
            cv2.line(img5, 
                     (int(pt1[0]), int(pt1[1])), 
                     (int(pt2[0]), int(pt2[1])), 
                     color, 1)
        
        # 绘制最小和最大Feret直径
        min_idx = np.argmin(feret_diameters)
        max_idx = np.argmax(feret_diameters)
        
        # 最小Feret直径（蓝色）
        pt1_min, pt2_min = feret_lines[min_idx]
        cv2.line(img5, 
                 (int(pt1_min[0]), int(pt1_min[1])), 
                 (int(pt2_min[0]), int(pt2_min[1])), 
                 (255, 0, 0), 2)
        
        # 最大Feret直径（红色）
        pt1_max, pt2_max = feret_lines[max_idx]
        cv2.line(img5, 
                 (int(pt1_max[0]), int(pt1_max[1])), 
                 (int(pt2_max[0]), int(pt2_max[1])), 
                 (0, 0, 255), 2)
        
        # 添加文本
        cv2.putText(img5, f"Min Feret: {features['min_feret_diameter']:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        cv2.putText(img5, f"Max Feret: {features['max_feret_diameter']:.1f}", (10, 60), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.putText(img5, f"Feret Ratio: {features['feret_ratio']:.2f}", (10, 90), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    
    axes[1, 0].imshow(cv2.cvtColor(img5, cv2.COLOR_BGR2RGB))
    axes[1, 0].set_title("Feret直径")
    axes[1, 0].axis('off')
    
    # 子图6: 多边形逼近
    img6 = color_image.copy()
    cv2.drawContours(img6, [approx], -1, (255, 0, 255), 2)
    cv2.putText(img6, f"Polygon Sides: {features['polygon_sides']}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
    axes[1, 1].imshow(cv2.cvtColor(img6, cv2.COLOR_BGR2RGB))
    axes[1, 1].set_title("多边形逼近")
    axes[1, 1].axis('off')
    
    # 子图7: 特征汇总表
    ax_table = axes[1, 2]
    ax_table.axis('off')
    
    # 准备表格数据
    table_data = [
        ["特征", "值"],
        ["面积", f"{features.get('area', 0):.0f}"],
        ["周长", f"{features.get('perimeter', 0):.1f}"],
        ["似圆度", f"{features.get('circularity', 0):.3f}"],
        ["长宽比", f"{features.get('aspect_ratio', 0):.2f}"],
        ["凸度", f"{features.get('convexity', 0):.3f}"],
        ["孔洞数", f"{features.get('num_holes', 0)}"],
        ["多边形边数", f"{features.get('polygon_sides', 0)}"],
        ["紧密度", f"{features.get('compactness', 0):.3f}"],
        ["矩形度", f"{features.get('rectangularity', 0):.3f}"],
        ["Feret比", f"{features.get('feret_ratio', 0):.2f}"]
    ]
    
    table = ax_table.table(cellText=table_data, 
                          cellLoc='center', 
                          loc='center',
                          colWidths=[0.3, 0.3])
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 1.5)
    ax_table.set_title("特征汇总")
    
    # 子图8: Hu矩可视化
    ax_hu = axes[1, 3]
    if 'hu_moment_1' in features:
        hu_values = [features[f'hu_moment_{i+1}'] for i in range(7)]
        ax_hu.bar(range(1, 8), hu_values, color='skyblue')
        ax_hu.set_title("Hu不变矩")
        ax_hu.set_xlabel("矩序号")
        ax_hu.set_ylabel("值")
        ax_hu.grid(True, linestyle='--', alpha=0.6)
    else:
        ax_hu.axis('off')
        ax_hu.text(0.5, 0.5, "Hu矩不可用", 
                  horizontalalignment='center',
                  verticalalignment='center',
                  transform=ax_hu.transAxes)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    plt.show()
    
    return features

# 创建不同形状的测试图像
def create_test_shapes():
    # 创建一个空白画布
    canvas = np.zeros((600, 800), dtype=np.uint8)
    
    # 创建不同形状
    # 1. 圆形
    cv2.circle(canvas, (150, 150), 80, 255, -1)
    
    # 2. 矩形
    cv2.rectangle(canvas, (300, 80), (500, 220), 255, -1)
    
    # 3. 椭圆
    cv2.ellipse(canvas, ((650, 150), (100, 60), 30, 0, 360, 255, -1))
    
    # 4. 星形（带孔洞）
    star = np.zeros((200, 200), dtype=np.uint8)
    points = np.array([[100, 20], [140, 80], [200, 80], [150, 120], 
                      [180, 180], [100, 140], [20, 180], [50, 120], 
                      [0, 80], [60, 80]], dtype=np.int32)
    cv2.fillPoly(star, [points], 255)
    cv2.circle(star, (100, 100), 30, 0, -1)  # 添加孔洞
    canvas[400:600, 100:300] = star
    
    # 5. 三角形
    triangle = np.zeros((200, 200), dtype=np.uint8)
    tri_points = np.array([[100, 20], [180, 180], [20, 180]], dtype=np.int32)
    cv2.fillPoly(triangle, [tri_points], 255)
    canvas[400:600, 400:600] = triangle
    
    # 6. 不规则形状
    irregular = np.zeros((200, 200), dtype=np.uint8)
    irr_points = np.array([[30, 30], [150, 20], [180, 80], [160, 150], 
                          [100, 180], [40, 150], [20, 100]], dtype=np.int32)
    cv2.fillPoly(irregular, [irr_points], 255)
    canvas[400:600, 650:850] = irregular
    
    return canvas

# 主函数
if __name__ == "__main__":
    # 创建测试图像
    #test_image = create_test_shapes()
    # 读取图像并二值化
    image = cv2.imread('000000.jpg', cv2.IMREAD_GRAYSCALE)
    _, binary_image = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)

    # 提取特征并可视化
    features = extract_and_visualize_geometric_features(binary_image, "自定义图像分析")
    
    # 提取所有连通区域
    contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 为每个形状提取特征并可视化
    for i, contour in enumerate(contours):
        # 为每个形状创建独立的二值图像
        shape_img = np.zeros_like(image)
        cv2.drawContours(shape_img, [contour], -1, 255, -1)
        
        # 提取特征并可视化
        shape_features = extract_and_visualize_geometric_features(
            shape_img, 
            title=f"形状 {i+1} 的几何特征分析"
        )
        print(f"形状 {i+1} 的特征: {shape_features}")
    # 关闭所有OpenCV窗口    
    cv2.destroyAllWindows()
    plt.close('all')