import torch
import cv2
import numpy as np
from torchvision import transforms

def preprocess_image(image, device):
    # 调整图像大小并转换为张量
    transform = transforms.Compose([
        transforms.ToPILImage(),
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 确保图像是RGB格式
    if len(image.shape) == 2:
        image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
    else:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 应用转换并移至设备
    tensor = transform(image).unsqueeze(0)
    return tensor.to(device)

def postprocess_saliency(saliency_map):
    # 将显著性图转换为二值掩码
    saliency_map = saliency_map.cpu().detach().squeeze().numpy()
    saliency_map = (saliency_map * 255).astype(np.uint8)
    
    # 阈值处理
    _, mask = cv2.threshold(saliency_map, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 形态学操作优化掩码
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    return mask

def visualize_anisotropy(image, anisotropy):
    # 将各向异性分析结果可视化
    intensity = anisotropy[:, 0:1, :, :].cpu().detach().squeeze().numpy()
    orientation = anisotropy[:, 1:2, :, :].cpu().detach().squeeze().numpy()
    
    # 调整大小以匹配原始图像
    h, w = image.shape
    intensity = cv2.resize(intensity, (w, h))
    orientation = cv2.resize(orientation, (w, h))
    
    # 创建彩色可视化
    hsv = np.zeros((h, w, 3), dtype=np.uint8)
    
    # 色调表示方向 (0-180度)
    hsv[:, :, 0] = ((orientation + np.pi/2) * 180 / np.pi).astype(np.uint8)
    
    # 饱和度表示各向异性强度
    hsv[:, :, 1] = (intensity * 255).astype(np.uint8)
    
    # 值通道表示原始图像强度
    hsv[:, :, 2] = cv2.normalize(image, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
    
    # 转换为BGR颜色空间
    bgr = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
    
    # 添加方向向量可视化
    step = max(1, min(h, w) // 50)  # 向量间距
    for i in range(0, h, step):
        for j in range(0, w, step):
            if intensity[i, j] > 0.3:  # 只显示强度足够的区域
                angle = orientation[i, j]
                length = intensity[i, j] * step * 0.7
                
                # 计算向量终点
                end_i = int(i + length * np.sin(angle))
                end_j = int(j + length * np.cos(angle))
                
                # 确保终点在图像范围内
                end_i = max(0, min(end_i, h-1))
                end_j = max(0, min(end_j, w-1))
                
                # 绘制向量
                cv2.line(bgr, (j, i), (end_j, end_i), (0, 0, 255), 1)
    
    return bgr    #!/usr/bin/env wolframscript
