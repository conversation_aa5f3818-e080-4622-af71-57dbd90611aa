import torch
import torch.nn as nn
import torch.nn.functional as F

class SaliencyDetector(nn.Module):
    def __init__(self):
        super(SaliencyDetector, self).__init__()
        # 红外图像特征提取
        self.ir_encoder = nn.Sequential(
            nn.Conv2d(3, 32, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.ReLU()
        )
        
        # 可见光图像特征提取
        self.visible_encoder = nn.Sequential(
            nn.Conv2d(3, 32, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.ReLU()
        )
        
        # 特征融合和显著性预测
        self.fusion = nn.Sequential(
            nn.Conv2d(128, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(64, 32, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 注意力机制
        self.attention = nn.Sequential(
            nn.Conv2d(256, 32, kernel_size=1),
            nn.ReLU(),
            nn.Conv2d(32, 2, kernel_size=1),
            nn.Softmax(dim=1)
        )
    
    def forward(self, ir_image, visible_image=None):
        ir_features = self.ir_encoder(ir_image)
        
        if visible_image is not None:
            visible_features = self.visible_encoder(visible_image)
            
            # 特征融合
            concat_features = torch.cat([ir_features, visible_features], dim=1)
            
            # 计算注意力权重
            att_weights = self.attention(concat_features)
            weighted_ir = ir_features * att_weights[:, 0:1, :, :]
            weighted_visible = visible_features * att_weights[:, 1:2, :, :]
            
            fused_features = weighted_ir + weighted_visible
        else:
            fused_features = ir_features
        
        # 生成显著性图
        saliency_map = self.fusion(fused_features)
        
        # 上采样到原始尺寸
        saliency_map = F.interpolate(saliency_map, size=ir_image.shape[2:], mode='bilinear', align_corners=False)
        
        return saliency_map

class AnisotropyAnalyzer(nn.Module):
    def __init__(self):
        super(AnisotropyAnalyzer, self).__init__()
        # 特征提取网络
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(1, 32, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.ReLU()
        )
        
        # 方向分析网络
        self.direction_analysis = nn.Sequential(
            nn.Conv2d(128, 64, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(64, 32, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 8, kernel_size=1)  # 8个方向通道
        )
        
        # 各向异性参数估计
        self.anisotropy_estimation = nn.Sequential(
            nn.Conv2d(8, 4, kernel_size=3, stride=1, padding=1),
            nn.ReLU(),
            nn.Conv2d(4, 2, kernel_size=1)  # 各向异性强度和方向
        )
    
    def forward(self, x):
        # 确保输入是四维张量
        if x.dim() == 2:
            x = x.unsqueeze(0).unsqueeze(0)
        
        # 特征提取
        features = self.feature_extractor(x)
        
        # 方向分析
        directions = self.direction_analysis(features)
        
        # 各向异性参数估计
        anisotropy = self.anisotropy_estimation(directions)
        
        # 各向异性强度归一化
        intensity = torch.sigmoid(anisotropy[:, 0:1, :, :])
        orientation = torch.tanh(anisotropy[:, 1:2, :, :]) * torch.pi / 2.0
        
        return torch.cat([intensity, orientation], dim=1)    