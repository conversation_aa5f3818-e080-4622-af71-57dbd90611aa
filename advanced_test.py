"""
图像分析系统高级测试脚本
"""

import cv2
import numpy as np
import os
import json
from datetime import datetime
from image_analysis_system import ImageAnalysisSystem
import config

class AdvancedTester:
    """高级测试类"""
    
    def __init__(self):
        self.system = ImageAnalysisSystem()
        self.test_results = []
        
    def create_test_scenarios(self):
        """创建多种测试场景"""
        scenarios = []
        
        # 场景1：晴天场景
        scenarios.append({
            'name': '晴天场景测试',
            'image': self.create_sunny_scene(),
            'scene_description': '晴天',
            'individual_bbox': (150, 100, 200, 150),
            'component_bbox': (200, 125, 60, 50),
            'entity_type': '电科一号',
            'component_name': '雷达'
        })
        
        # 场景2：雨天场景
        scenarios.append({
            'name': '雨天场景测试',
            'image': self.create_rainy_scene(),
            'scene_description': '雨天',
            'individual_bbox': (120, 120, 180, 140),
            'component_bbox': (170, 140, 50, 40),
            'entity_type': '电科一号',
            'component_name': '雷达'
        })
        
        # 场景3：夜晚场景
        scenarios.append({
            'name': '夜晚场景测试',
            'image': self.create_night_scene(),
            'scene_description': '夜晚',
            'individual_bbox': (100, 80, 220, 160),
            'component_bbox': (180, 100, 70, 55),
            'entity_type': '电科一号',
            'component_name': '雷达'
        })
        
        return scenarios
    
    def create_sunny_scene(self):
        """创建晴天场景图像"""
        image = np.ones((480, 640, 3), dtype=np.uint8) * 200
        
        # 添加明亮的背景
        for i in range(480):
            for j in range(640):
                image[i, j] = [180 + np.random.randint(-20, 20),
                              200 + np.random.randint(-15, 15),
                              220 + np.random.randint(-10, 10)]
        
        # 添加目标物体
        self.add_target_objects(image, brightness_factor=1.2)
        
        return image
    
    def create_rainy_scene(self):
        """创建雨天场景图像"""
        image = np.ones((480, 640, 3), dtype=np.uint8) * 120
        
        # 添加较暗的背景
        for i in range(480):
            for j in range(640):
                image[i, j] = [100 + np.random.randint(-30, 30),
                              120 + np.random.randint(-25, 25),
                              140 + np.random.randint(-20, 20)]
        
        # 添加雨滴效果
        for _ in range(1000):
            x, y = np.random.randint(0, 640), np.random.randint(0, 480)
            cv2.line(image, (x, y), (x+2, y+8), (200, 200, 200), 1)
        
        # 添加目标物体
        self.add_target_objects(image, brightness_factor=0.8)
        
        return image
    
    def create_night_scene(self):
        """创建夜晚场景图像"""
        image = np.ones((480, 640, 3), dtype=np.uint8) * 50
        
        # 添加很暗的背景
        for i in range(480):
            for j in range(640):
                image[i, j] = [30 + np.random.randint(-20, 40),
                              40 + np.random.randint(-25, 45),
                              60 + np.random.randint(-30, 50)]
        
        # 添加一些光源
        for _ in range(10):
            x, y = np.random.randint(50, 590), np.random.randint(50, 430)
            cv2.circle(image, (x, y), np.random.randint(5, 15), (255, 255, 200), -1)
        
        # 添加目标物体
        self.add_target_objects(image, brightness_factor=1.5)
        
        return image
    
    def add_target_objects(self, image, brightness_factor=1.0):
        """在图像中添加目标物体"""
        # 个体区域
        individual_color = tuple(int(c * brightness_factor) for c in [80, 120, 160])
        cv2.rectangle(image, (150, 100), (350, 250), individual_color, -1)
        cv2.rectangle(image, (150, 100), (350, 250), (0, 255, 0), 2)
        
        # 部件区域
        component_color = tuple(int(c * brightness_factor) for c in [200, 100, 100])
        cv2.circle(image, (250, 175), 30, component_color, -1)
        cv2.circle(image, (250, 175), 30, (255, 0, 0), 2)
        
        # 添加一些细节
        cv2.rectangle(image, (200, 150), (300, 200), 
                     tuple(int(c * brightness_factor * 0.8) for c in [150, 150, 150]), -1)
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("=== 图像分析系统综合测试 ===")
        
        scenarios = self.create_test_scenarios()
        
        for i, scenario in enumerate(scenarios):
            print(f"\n--- 测试场景 {i+1}: {scenario['name']} ---")
            
            # 保存测试图像
            image_path = f"test_scenario_{i+1}.jpg"
            cv2.imwrite(image_path, scenario['image'])
            
            try:
                # 执行分析
                results = self.system.process_image(
                    image_path=image_path,
                    scene_description=scenario['scene_description'],
                    individual_bbox=scenario['individual_bbox'],
                    component_bbox=scenario['component_bbox'],
                    entity_type=scenario['entity_type'],
                    component_name=scenario['component_name']
                )
                
                # 记录测试结果
                test_result = {
                    'scenario_name': scenario['name'],
                    'timestamp': datetime.now().isoformat(),
                    'scene_match_score': float(results['scene_comparison']['match_score']),
                    'scene_is_match': bool(results['scene_comparison']['is_match']),
                    'geometric_features': {k: float(v) if isinstance(v, (np.float32, np.float64)) else v
                                         for k, v in results['geometric_features'].items()},
                    'component_belongs': bool(results['component_relations']['belongs_to_entity']),
                    'related_components_count': int(len(results['related_bboxes'])),
                    'success': True
                }
                
                self.test_results.append(test_result)
                
                # 显示关键结果
                print(f"   场景匹配度: {results['scene_comparison']['match_score']:.4f}")
                print(f"   场景匹配: {'是' if results['scene_comparison']['is_match'] else '否'}")
                print(f"   长宽比: {results['geometric_features']['aspect_ratio']:.4f}")
                print(f"   圆度: {results['geometric_features']['circularity']:.4f}")
                print(f"   部件属于个体: {'是' if results['component_relations']['belongs_to_entity'] else '否'}")
                print(f"   相关部件数量: {len(results['related_bboxes'])}")
                
                # 保存结果图像
                result_path = f"result_scenario_{i+1}.jpg"
                cv2.imwrite(result_path, results['result_image'])
                print(f"   结果图像已保存: {result_path}")
                
            except Exception as e:
                print(f"   测试失败: {e}")
                test_result = {
                    'scenario_name': scenario['name'],
                    'timestamp': datetime.now().isoformat(),
                    'error': str(e),
                    'success': False
                }
                self.test_results.append(test_result)
        
        # 生成测试报告
        self.generate_test_report()
    
    def generate_test_report(self):
        """生成测试报告"""
        report = {
            'test_summary': {
                'total_tests': len(self.test_results),
                'successful_tests': sum(1 for r in self.test_results if r['success']),
                'failed_tests': sum(1 for r in self.test_results if not r['success']),
                'timestamp': datetime.now().isoformat()
            },
            'test_results': self.test_results
        }
        
        # 保存JSON报告
        with open('test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        print(f"\n=== 测试报告摘要 ===")
        print(f"总测试数: {report['test_summary']['total_tests']}")
        print(f"成功测试: {report['test_summary']['successful_tests']}")
        print(f"失败测试: {report['test_summary']['failed_tests']}")
        print(f"成功率: {report['test_summary']['successful_tests']/report['test_summary']['total_tests']*100:.1f}%")
        print(f"详细报告已保存到: test_report.json")
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("\n=== 边界情况测试 ===")
        
        edge_cases = [
            {
                'name': '空图像测试',
                'image': np.zeros((100, 100, 3), dtype=np.uint8),
                'bbox': (10, 10, 50, 50)
            },
            {
                'name': '纯白图像测试',
                'image': np.ones((100, 100, 3), dtype=np.uint8) * 255,
                'bbox': (20, 20, 40, 40)
            },
            {
                'name': '高噪声图像测试',
                'image': np.random.randint(0, 256, (200, 200, 3), dtype=np.uint8),
                'bbox': (50, 50, 100, 100)
            }
        ]
        
        for case in edge_cases:
            print(f"\n--- {case['name']} ---")
            try:
                features = self.system.extract_image_features(case['image'])
                geo_features = self.system.extract_geometric_features(case['image'], case['bbox'])
                print(f"   特征提取成功")
                print(f"   熵值: {features['entropy']:.4f}")
                print(f"   对比度: {features['contrast']:.4f}")
                print(f"   圆度: {geo_features['circularity']:.4f}")
            except Exception as e:
                print(f"   测试失败: {e}")
    
    def performance_test(self):
        """性能测试"""
        print("\n=== 性能测试 ===")
        
        import time
        
        # 创建不同尺寸的测试图像
        sizes = [(480, 640), (720, 1280), (1080, 1920)]
        
        for size in sizes:
            print(f"\n--- 图像尺寸: {size[0]}x{size[1]} ---")
            
            # 创建测试图像
            test_image = np.random.randint(0, 256, (size[0], size[1], 3), dtype=np.uint8)
            bbox = (size[1]//4, size[0]//4, size[1]//2, size[0]//2)
            
            # 测试特征提取时间
            start_time = time.time()
            features = self.system.extract_image_features(test_image)
            feature_time = time.time() - start_time
            
            # 测试几何特征提取时间
            start_time = time.time()
            geo_features = self.system.extract_geometric_features(test_image, bbox)
            geo_time = time.time() - start_time
            
            print(f"   特征提取时间: {feature_time:.4f}秒")
            print(f"   几何特征提取时间: {geo_time:.4f}秒")
            print(f"   总处理时间: {feature_time + geo_time:.4f}秒")

def main():
    """主测试函数"""
    tester = AdvancedTester()
    
    # 运行综合测试
    tester.run_comprehensive_test()
    
    # 运行边界情况测试
    tester.test_edge_cases()
    
    # 运行性能测试
    tester.performance_test()
    
    print("\n=== 所有测试完成 ===")

if __name__ == "__main__":
    main()
