#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RDF文件读取和解析工具
支持读取OWL/RDF格式的本体文件
"""

import rdflib
from rdflib import Graph, Namespace, RDF, RDFS, OWL
from collections import defaultdict
import argparse
import sys

def read_rdf_file(file_path):
    """
    读取RDF文件并解析内容
    
    Args:
        file_path (str): RDF文件路径
    
    Returns:
        rdflib.Graph: 解析后的RDF图
    """
    try:
        # 创建RDF图
        g = Graph()
        
        # 解析RDF文件
        g.parse(file_path, format="xml")
        
        print(f"成功读取RDF文件: {file_path}")
        print(f"三元组数量: {len(g)}")
        
        return g
    
    except Exception as e:
        print(f"读取RDF文件时出错: {e}")
        return None

def analyze_ontology_structure(graph):
    """
    分析本体结构
    
    Args:
        graph (rdflib.Graph): RDF图
    """
    print("\n=== 本体结构分析 ===")
    
    # 统计各种类型的实体
    classes = set()
    object_properties = set()
    data_properties = set()
    individuals = set()
    
    for s, p, o in graph:
        if p == RDF.type:
            if o == OWL.Class:
                classes.add(s)
            elif o == OWL.ObjectProperty:
                object_properties.add(s)
            elif o == OWL.DatatypeProperty:
                data_properties.add(s)
            elif o != OWL.Ontology:
                individuals.add(s)
    
    print(f"类 (Classes): {len(classes)}")
    print(f"对象属性 (Object Properties): {len(object_properties)}")
    print(f"数据属性 (Data Properties): {len(data_properties)}")
    print(f"个体 (Individuals): {len(individuals)}")

def show_classes(graph, limit=20):
    """
    显示本体中的类
    
    Args:
        graph (rdflib.Graph): RDF图
        limit (int): 显示数量限制
    """
    print(f"\n=== 类列表 (前{limit}个) ===")
    
    classes = []
    for s, p, o in graph:
        if p == RDF.type and o == OWL.Class:
            # 获取类的本地名称
            local_name = s.split('#')[-1] if '#' in str(s) else str(s).split('/')[-1]
            classes.append((local_name, s))
    
    classes.sort()
    for i, (local_name, uri) in enumerate(classes[:limit]):
        print(f"{i+1:2d}. {local_name}")

def show_properties(graph, limit=20):
    """
    显示本体中的属性
    
    Args:
        graph (rdflib.Graph): RDF图
        limit (int): 显示数量限制
    """
    print(f"\n=== 对象属性列表 (前{limit}个) ===")
    
    properties = []
    for s, p, o in graph:
        if p == RDF.type and o == OWL.ObjectProperty:
            local_name = s.split('#')[-1] if '#' in str(s) else str(s).split('/')[-1]
            properties.append((local_name, s))
    
    properties.sort()
    for i, (local_name, uri) in enumerate(properties[:limit]):
        print(f"{i+1:2d}. {local_name}")
    
    print(f"\n=== 数据属性列表 (前{limit}个) ===")
    
    data_properties = []
    for s, p, o in graph:
        if p == RDF.type and o == OWL.DatatypeProperty:
            local_name = s.split('#')[-1] if '#' in str(s) else str(s).split('/')[-1]
            data_properties.append((local_name, s))
    
    data_properties.sort()
    for i, (local_name, uri) in enumerate(data_properties[:limit]):
        print(f"{i+1:2d}. {local_name}")

def show_class_hierarchy(graph, limit=10):
    """
    显示类层次结构
    
    Args:
        graph (rdflib.Graph): RDF图
        limit (int): 显示数量限制
    """
    print(f"\n=== 类层次结构 (前{limit}个) ===")
    
    hierarchy = defaultdict(list)
    
    # 查找子类关系
    for s, p, o in graph:
        if p == RDFS.subClassOf:
            parent_name = o.split('#')[-1] if '#' in str(o) else str(o).split('/')[-1]
            child_name = s.split('#')[-1] if '#' in str(s) else str(s).split('/')[-1]
            hierarchy[parent_name].append(child_name)
    
    count = 0
    for parent, children in list(hierarchy.items())[:limit]:
        print(f"{parent}:")
        for child in children:
            print(f"  └── {child}")
        count += 1
        if count >= limit:
            break

def search_by_keyword(graph, keyword):
    """
    根据关键词搜索相关实体
    
    Args:
        graph (rdflib.Graph): RDF图
        keyword (str): 搜索关键词
    """
    print(f"\n=== 搜索结果: '{keyword}' ===")
    
    results = []
    
    for s, p, o in graph:
        # 搜索主语
        if keyword in str(s):
            local_name = s.split('#')[-1] if '#' in str(s) else str(s).split('/')[-1]
            if keyword in local_name:
                results.append(('实体', local_name, s))
        
        # 搜索宾语（如果是字符串）
        if isinstance(o, rdflib.Literal) and keyword in str(o):
            subject_name = s.split('#')[-1] if '#' in str(s) else str(s).split('/')[-1]
            results.append(('字面值', f"{subject_name} -> {o}", s))
    
    # 去重并排序
    results = list(set(results))
    results.sort()
    
    if results:
        for entity_type, description, uri in results[:20]:  # 限制显示20个结果
            print(f"[{entity_type}] {description}")
    else:
        print("未找到相关结果")

def main():
    parser = argparse.ArgumentParser(description='RDF文件读取和分析工具')
    parser.add_argument('file', help='RDF文件路径')
    parser.add_argument('--search', '-s', help='搜索关键词')
    parser.add_argument('--limit', '-l', type=int, default=20, help='显示数量限制')
    
    args = parser.parse_args()
    
    # 读取RDF文件
    graph = read_rdf_file(args.file)
    if graph is None:
        sys.exit(1)
    
    # 分析本体结构
    analyze_ontology_structure(graph)
    
    # 显示类和属性
    show_classes(graph, args.limit)
    show_properties(graph, args.limit)
    
    # 显示类层次结构
    show_class_hierarchy(graph, args.limit)
    
    # 如果提供了搜索关键词，进行搜索
    if args.search:
        search_by_keyword(graph, args.search)

if __name__ == "__main__":
    # 如果没有命令行参数，使用默认文件
    if len(sys.argv) == 1:
        print("使用默认文件: knowledge.RDF")
        graph = read_rdf_file("knowledge.RDF")
        if graph:
            analyze_ontology_structure(graph)
            show_classes(graph)
            show_properties(graph)
            show_class_hierarchy(graph)
    else:
        main()
