import sys
import numpy as np
import cv2
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFileDialog, QSlider, 
                            QComboBox, QGroupBox, QGridLayout, QSplitter)
from PyQt5.QtGui import QImage, QPixmap, QFont
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from PyQt5.QtWidgets import QSizePolicy

# 设置中文字体支持
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]

class FeatureCalculator:
    """特征计算类，负责计算各种图像特征"""
    
    @staticmethod
    def calculate_structure(image):
        """计算结构特征"""
        # 使用拉普拉斯算子计算图像的二阶导数，用于评估图像的结构复杂度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        return laplacian_var
    
    @staticmethod
    def calculate_entropy(image):
        """计算信息熵"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist = hist.ravel() / hist.sum()
        entropy = -np.sum(hist * np.log2(hist + np.finfo(float).eps))
        return entropy
    
    @staticmethod
    def calculate_contrast(image):
        """计算对比度"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        mean = np.mean(gray)
        std = np.std(gray)
        contrast = std / (mean + np.finfo(float).eps)
        return contrast
    
    @staticmethod
    def calculate_gray_level(image):
        """计算灰度值"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        return np.mean(gray)
    
    @staticmethod
    def calculate_high_frequency(image):
        """计算高频复杂度"""
        # 使用傅里叶变换计算高频分量的能量
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        f = np.fft.fft2(gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift))
        
        # 计算高频区域的能量
        rows, cols = gray.shape
        crow, ccol = rows // 2, cols // 2
        fshift[crow-30:crow+30, ccol-30:ccol+30] = 0  # 去除低频部分
        f_ishift = np.fft.ifftshift(fshift)
        img_back = np.fft.ifft2(f_ishift)
        img_back = np.abs(img_back)
        
        return np.mean(img_back)
    
    @staticmethod
    def calculate_energy(image):
        """计算能量特征"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        return np.mean(gray ** 2)
    
    @staticmethod
    def normalize_features(features):
        """归一化特征向量"""
        if not features:
            return []
        # 转置特征矩阵，使得每一列是一个特征的所有值
        feature_matrix = np.array(features).T
        normalized_matrix = []
        
        for feature_values in feature_matrix:
            if np.max(feature_values) == np.min(feature_values):
                # 处理所有值相同的情况，避免除零错误
                normalized_values = np.ones_like(feature_values) / len(feature_values)
            else:
                # 最小-最大归一化
                normalized_values = (feature_values - np.min(feature_values)) / (np.max(feature_values) - np.min(feature_values))
            normalized_matrix.append(normalized_values)
        
        # 转置回原来的形状
        return np.array(normalized_matrix).T.tolist()
    
    @staticmethod
    def calculate_weights(visible_features, ir_features, feature_weights=None):
        """计算可见光和红外的最终权重"""
        if feature_weights is None:
            # 默认权重，所有特征同等重要
            feature_weights = [1.0] * len(visible_features)
        
        # 归一化特征
        normalized_features = FeatureCalculator.normalize_features([visible_features, ir_features])
        
        # 计算加权和
        visible_score = sum(w * f for w, f in zip(feature_weights, normalized_features[0]))
        ir_score = sum(w * f for w, f in zip(feature_weights, normalized_features[1]))
        
        # 归一化权重
        total_score = visible_score + ir_score
        if total_score == 0:
            return [0.5, 0.5]  # 避免除零错误
        return [visible_score / total_score, ir_score / total_score]


class ImageProcessingThread(QThread):
    """图像处理线程，用于在后台处理图像并计算特征"""
    update_signal = pyqtSignal(object, object, object, object)
    
    def __init__(self, visible_frame, ir_frame, feature_calculator):
        super().__init__()
        self.visible_frame = visible_frame
        self.ir_frame = ir_frame
        self.feature_calculator = feature_calculator
        
    def run(self):
        if self.visible_frame is None or self.ir_frame is None:
            self.update_signal.emit(None, None, None, None)
            return
            
        # 计算特征
        visible_features = [
            self.feature_calculator.calculate_structure(self.visible_frame),
            self.feature_calculator.calculate_entropy(self.visible_frame),
            self.feature_calculator.calculate_gray_level(self.visible_frame),
            self.feature_calculator.calculate_contrast(self.visible_frame),
            self.feature_calculator.calculate_high_frequency(self.visible_frame),
            self.feature_calculator.calculate_energy(self.visible_frame)
        ]
        
        ir_features = [
            self.feature_calculator.calculate_structure(self.ir_frame),
            self.feature_calculator.calculate_entropy(self.ir_frame),
            self.feature_calculator.calculate_gray_level(self.ir_frame),
            self.feature_calculator.calculate_contrast(self.ir_frame),
            self.feature_calculator.calculate_high_frequency(self.ir_frame),
            self.feature_calculator.calculate_energy(self.ir_frame)
        ]
        
        # 计算权重
        weights = self.feature_calculator.calculate_weights(visible_features, ir_features)
        
        # 归一化特征
        normalized_features = self.feature_calculator.normalize_features([visible_features, ir_features])
        
        self.update_signal.emit(visible_features, ir_features, normalized_features, weights)

class MatplotlibCanvas(FigureCanvas):
    """Matplotlib画布，用于在PyQt5中显示图表"""
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MatplotlibCanvas, self).__init__(self.fig)
        self.setParent(parent)
        FigureCanvas.setSizePolicy(self, QSizePolicy.Expanding, QSizePolicy.Expanding)
        FigureCanvas.updateGeometry(self)


class MainWindow(QMainWindow):
    """主窗口类，负责UI界面的创建和管理"""
    
    def __init__(self):
        super().__init__()
        
        self.visible_frame = None
        self.ir_frame = None
        self.visible_path = None
        self.ir_path = None
        self.is_video = False
        self.cap_visible = None
        self.cap_ir = None
        self.timer = QTimer(self)
        self.feature_calculator = FeatureCalculator()
        self.processing_thread = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('可见光与红外图像/视频特征权重计算器')
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 图像选项卡
        image_tab = QWidget()
        image_layout = QVBoxLayout(image_tab)
        self.setup_image_tab(image_layout)
        tab_widget.addTab(image_tab, "图像模式")
        
        # 视频选项卡
        video_tab = QWidget()
        video_layout = QVBoxLayout(video_tab)
        self.setup_video_tab(video_layout)
        tab_widget.addTab(video_tab, "视频模式")
        
        main_layout.addWidget(tab_widget)
        
        # 状态栏
        self.statusBar().showMessage('就绪')
        
    def setup_image_tab(self, layout):
        """设置图像模式选项卡"""
        # 文件选择区域
        file_select_layout = QHBoxLayout()
        
        visible_file_layout = QVBoxLayout()
        visible_file_label = QLabel("可见光图像:")
        self.visible_image_label = QLabel("未选择图像")
        visible_file_button = QPushButton("选择图像")
        visible_file_button.clicked.connect(lambda: self.select_file(True, False))
        
        visible_file_layout.addWidget(visible_file_label)
        visible_file_layout.addWidget(self.visible_image_label)
        visible_file_layout.addWidget(visible_file_button)
        
        ir_file_layout = QVBoxLayout()
        ir_file_label = QLabel("红外图像:")
        self.ir_image_label = QLabel("未选择图像")
        ir_file_button = QPushButton("选择图像")
        ir_file_button.clicked.connect(lambda: self.select_file(False, False))
        
        ir_file_layout.addWidget(ir_file_label)
        ir_file_layout.addWidget(self.ir_image_label)
        ir_file_layout.addWidget(ir_file_button)
        
        process_button = QPushButton("处理图像")
        process_button.clicked.connect(self.process_image)
        
        file_select_layout.addLayout(visible_file_layout)
        file_select_layout.addLayout(ir_file_layout)
        file_select_layout.addWidget(process_button)
        
        # 图像显示区域
        image_display_layout = QHBoxLayout()
        
        self.visible_display = QLabel("可见光图像预览")
        self.visible_display.setAlignment(Qt.AlignCenter)
        self.visible_display.setMinimumSize(300, 300)
        self.visible_display.setStyleSheet("border: 1px solid #cccccc;")
        
        self.ir_display = QLabel("红外图像预览")
        self.ir_display.setAlignment(Qt.AlignCenter)
        self.ir_display.setMinimumSize(300, 300)
        self.ir_display.setStyleSheet("border: 1px solid #cccccc;")
        
        image_display_layout.addWidget(self.visible_display)
        image_display_layout.addWidget(self.ir_display)
        
        # 特征权重设置区域
        feature_weights_layout = QGridLayout()
        feature_weights_group = QGroupBox("特征权重设置")
        feature_weights_group.setLayout(feature_weights_layout)
        
        self.feature_weight_sliders = []
        features = ["结构", "信息熵", "灰度", "对比度", "高频复杂度", "能量"]
        
        for i, feature in enumerate(features):
            label = QLabel(feature + "权重:")
            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(0)
            slider.setMaximum(100)
            slider.setValue(100)  # 默认权重为1
            slider.valueChanged.connect(self.update_feature_weights)
            
            value_label = QLabel("1.0")
            value_label.setFixedWidth(30)
            
            feature_weights_layout.addWidget(label, i, 0)
            feature_weights_layout.addWidget(slider, i, 1)
            feature_weights_layout.addWidget(value_label, i, 2)
            
            self.feature_weight_sliders.append((slider, value_label))
        
        # 可视化区域
        visualization_layout = QHBoxLayout()
        
        self.feature_bar_canvas = MatplotlibCanvas(self, width=6, height=4, dpi=100)
        self.feature_bar_canvas.axes.set_title('特征量化结果')
        
        self.weight_pie_canvas = MatplotlibCanvas(self, width=4, height=4, dpi=100)
        self.weight_pie_canvas.axes.set_title('模型分配权重')
        
        visualization_layout.addWidget(self.feature_bar_canvas)
        visualization_layout.addWidget(self.weight_pie_canvas)
        
        # 添加所有布局到主布局
        layout.addLayout(file_select_layout)
        layout.addLayout(image_display_layout)
        layout.addWidget(feature_weights_group)
        layout.addLayout(visualization_layout)
    
    def setup_video_tab(self, layout):
        """设置视频模式选项卡"""
        # 文件选择区域
        file_select_layout = QHBoxLayout()
        
        visible_file_layout = QVBoxLayout()
        visible_file_label = QLabel("可见光视频:")
        self.visible_video_label = QLabel("未选择视频")
        visible_file_button = QPushButton("选择视频")
        visible_file_button.clicked.connect(lambda: self.select_file(True, True))
        
        visible_file_layout.addWidget(visible_file_label)
        visible_file_layout.addWidget(self.visible_video_label)
        visible_file_layout.addWidget(visible_file_button)
        
        ir_file_layout = QVBoxLayout()
        ir_file_label = QLabel("红外视频:")
        self.ir_video_label = QLabel("未选择视频")
        ir_file_button = QPushButton("选择视频")
        ir_file_button.clicked.connect(lambda: self.select_file(False, True))
        
        ir_file_layout.addWidget(ir_file_label)
        ir_file_layout.addWidget(self.ir_video_label)
        ir_file_layout.addWidget(ir_file_button)
        
        file_select_layout.addLayout(visible_file_layout)
        file_select_layout.addLayout(ir_file_layout)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        
        self.play_button = QPushButton("播放")
        self.play_button.clicked.connect(self.play_video)
        self.play_button.setEnabled(False)
        
        self.pause_button = QPushButton("暂停")
        self.pause_button.clicked.connect(self.pause_video)
        self.pause_button.setEnabled(False)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.stop_video)
        self.stop_button.setEnabled(False)
        
        self.fps_label = QLabel("FPS:")
        self.fps_value_label = QLabel("0")
        
        control_layout.addWidget(self.play_button)
        control_layout.addWidget(self.pause_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addWidget(self.fps_label)
        control_layout.addWidget(self.fps_value_label)
        control_layout.addStretch()
        
        # 视频显示区域
        video_display_layout = QHBoxLayout()
        
        self.visible_video_display = QLabel("可见光视频预览")
        self.visible_video_display.setAlignment(Qt.AlignCenter)
        self.visible_video_display.setMinimumSize(300, 300)
        self.visible_video_display.setStyleSheet("border: 1px solid #cccccc;")
        
        self.ir_video_display = QLabel("红外视频预览")
        self.ir_video_display.setAlignment(Qt.AlignCenter)
        self.ir_video_display.setMinimumSize(300, 300)
        self.ir_video_display.setStyleSheet("border: 1px solid #cccccc;")
        
        video_display_layout.addWidget(self.visible_video_display)
        video_display_layout.addWidget(self.ir_video_display)
        
        # 特征权重设置区域（与图像模式共享）
        feature_weights_layout = QGridLayout()
        feature_weights_group = QGroupBox("特征权重设置")
        feature_weights_group.setLayout(feature_weights_layout)
        
        features = ["结构", "信息熵", "灰度", "对比度", "高频复杂度", "能量"]
        
        for i, feature in enumerate(features):
            label = QLabel(feature + "权重:")
            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(0)
            slider.setMaximum(100)
            slider.setValue(100)  # 默认权重为1
            slider.valueChanged.connect(self.update_feature_weights)
            
            value_label = QLabel("1.0")
            value_label.setFixedWidth(30)
            
            feature_weights_layout.addWidget(label, i, 0)
            feature_weights_layout.addWidget(slider, i, 1)
            feature_weights_layout.addWidget(value_label, i, 2)
            
            if not hasattr(self, 'feature_weight_sliders'):
                self.feature_weight_sliders = []
            self.feature_weight_sliders.append((slider, value_label))
        
        # 可视化区域
        visualization_layout = QHBoxLayout()
        
        self.video_feature_bar_canvas = MatplotlibCanvas(self, width=6, height=4, dpi=100)
        self.video_feature_bar_canvas.axes.set_title('特征量化结果')
        
        self.video_weight_pie_canvas = MatplotlibCanvas(self, width=4, height=4, dpi=100)
        self.video_weight_pie_canvas.axes.set_title('模型分配权重')
        
        visualization_layout.addWidget(self.video_feature_bar_canvas)
        visualization_layout.addWidget(self.video_weight_pie_canvas)
        
        # 添加所有布局到主布局
        layout.addLayout(file_select_layout)
        layout.addLayout(control_layout)
        layout.addLayout(video_display_layout)
        layout.addWidget(feature_weights_group)
        layout.addLayout(visualization_layout)
    
    def select_file(self, is_visible, is_video):
        """选择文件对话框"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "", 
            "图像文件 (*.png *.jpg *.jpeg *.bmp);;视频文件 (*.mp4 *.avi *.mov)"
        )
        
        if file_path:
            if is_video:
                if is_visible:
                    self.visible_path = file_path
                    self.visible_video_label.setText(file_path)
                else:
                    self.ir_path = file_path
                    self.ir_video_label.setText(file_path)
                
                # 启用播放按钮
                if self.visible_path and self.ir_path:
                    self.play_button.setEnabled(True)
            else:
                if is_visible:
                    self.visible_path = file_path
                    self.visible_image_label.setText(file_path)
                    self.visible_frame = cv2.imread(file_path)
                    self.update_image_display(self.visible_display, self.visible_frame)
                else:
                    self.ir_path = file_path
                    self.ir_image_label.setText(file_path)
                    self.ir_frame = cv2.imread(file_path)
                    self.update_image_display(self.ir_display, self.ir_frame)
    
    def update_image_display(self, label, frame):
        """更新图像显示"""
        if frame is not None:
            rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            convert_to_Qt_format = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            p = convert_to_Qt_format.scaled(label.width(), label.height(), Qt.KeepAspectRatio)
            label.setPixmap(QPixmap.fromImage(p))
    
    def update_feature_weights(self):
        """更新特征权重显示"""
        for slider, value_label in self.feature_weight_sliders:
            weight = slider.value() / 100.0
            value_label.setText(f"{weight:.1f}")
    
    def get_current_feature_weights(self):
        """获取当前特征权重"""
        return [slider.value() / 100.0 for slider, _ in self.feature_weight_sliders]
    
    def process_image(self):
        """处理图像并计算特征"""
        if self.visible_frame is None or self.ir_frame is None:
            self.statusBar().showMessage('请先选择可见光和红外图像')
            return
            
        self.statusBar().showMessage('正在处理图像...')
        
        # 调整图像大小为相同尺寸
        h, w = self.visible_frame.shape[:2]
        ir_frame = cv2.resize(self.ir_frame, (w, h))
        
        # 创建图像处理线程
        self.processing_thread = ImageProcessingThread(self.visible_frame, ir_frame, self.feature_calculator)
        self.processing_thread.update_signal.connect(self.update_visualization)
        self.processing_thread.start()
    
    def play_video(self):
        """播放视频"""
        if self.cap_visible is not None and self.cap_visible.isOpened():
            self.cap_visible.release()
        if self.cap_ir is not None and self.cap_ir.isOpened():
            self.cap_ir.release()
            
        self.cap_visible = cv2.VideoCapture(self.visible_path)
        self.cap_ir = cv2.VideoCapture(self.ir_path)
        
        if not self.cap_visible.isOpened() or not self.cap_ir.isOpened():
            self.statusBar().showMessage('无法打开视频文件')
            return
        
        self.statusBar().showMessage('正在播放视频...')
        self.play_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        
        # 设置定时器
        self.timer.timeout.connect(self.update_video_frame)
        self.timer.start(30)  # 约30ms一帧，接近30FPS
    
    def pause_video(self):
        """暂停视频"""
        self.timer.stop()
        self.statusBar().showMessage('视频已暂停')
        self.play_button.setEnabled(True)
        self.pause_button.setEnabled(False)
    
    def stop_video(self):
        """停止视频"""
        self.timer.stop()
        if self.cap_visible is not None:
            self.cap_visible.release()
        if self.cap_ir is not None:
            self.cap_ir.release()
        self.statusBar().showMessage('视频已停止')
        self.play_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)
    
    def update_video_frame(self):
        """更新视频帧"""
        ret_vis, visible_frame = self.cap_visible.read()
        ret_ir, ir_frame = self.cap_ir.read()
        
        if not ret_vis or not ret_ir:
            # 视频结束
            self.stop_video()
            return
        
        # 调整图像大小为相同尺寸
        h, w = visible_frame.shape[:2]
        ir_frame = cv2.resize(ir_frame, (w, h))
        
        # 更新显示
        self.update_image_display(self.visible_video_display, visible_frame)
        self.update_image_display(self.ir_video_display, ir_frame)
        
        # 创建图像处理线程
        if self.processing_thread is None or not self.processing_thread.isRunning():
            self.processing_thread = ImageProcessingThread(visible_frame, ir_frame, self.feature_calculator)
            self.processing_thread.update_signal.connect(self.update_video_visualization)
            self.processing_thread.start()
    
    def update_visualization(self, visible_features, ir_features, normalized_features, weights):
        """更新可视化图表"""
        if visible_features is None or ir_features is None:
            self.statusBar().showMessage('图像处理失败')
            return
            
        self.statusBar().showMessage('图像处理完成')
        
        # 获取当前特征权重
        feature_weights = self.get_current_feature_weights()
        
        # 更新柱状图
        self.feature_bar_canvas.axes.clear()
        features_names = ["结构", "信息熵", "灰度", "对比度", "高频复杂度", "能量"]
        x = np.arange(len(features_names))
        width = 0.35
        
        self.feature_bar_canvas.axes.bar(x - width/2, normalized_features[0], width, label='可见光')
        self.feature_bar_canvas.axes.bar(x + width/2, normalized_features[1], width, label='红外')
        
        self.feature_bar_canvas.axes.set_ylabel('归一化值')
        self.feature_bar_canvas.axes.set_title('特征量化结果')
        self.feature_bar_canvas.axes.set_xticks(x)
        self.feature_bar_canvas.axes.set_xticklabels(features_names)
        self.feature_bar_canvas.axes.legend()
        
        self.feature_bar_canvas.fig.tight_layout()
        self.feature_bar_canvas.draw()
        
        # 更新饼图
        self.weight_pie_canvas.axes.clear()
        labels = '可见光', '红外'
        sizes = weights
        explode = (0.1, 0)  # 突出显示可见光部分
        
        self.weight_pie_canvas.axes.pie(sizes, explode=explode, labels=labels, autopct='%1.1f%%',
                shadow=True, startangle=90)
        self.weight_pie_canvas.axes.axis('equal')  # 保证饼图是圆的
        self.weight_pie_canvas.axes.set_title('模型分配权重')
        
        self.weight_pie_canvas.fig.tight_layout()
        self.weight_pie_canvas.draw()
    
    def update_video_visualization(self, visible_features, ir_features, normalized_features, weights):
        """更新视频模式下的可视化图表"""
        if visible_features is None or ir_features is None:
            return
            
        # 获取当前特征权重
        feature_weights = self.get_current_feature_weights()
        
        # 更新柱状图
        self.video_feature_bar_canvas.axes.clear()
        features_names = ["结构", "信息熵", "灰度", "对比度", "高频复杂度", "能量"]
        x = np.arange(len(features_names))
        width = 0.35
        
        self.video_feature_bar_canvas.axes.bar(x - width/2, normalized_features[0], width, label='可见光')
        self.video_feature_bar_canvas.axes.bar(x + width/2, normalized_features[1], width, label='红外')
        
        self.video_feature_bar_canvas.axes.set_ylabel('归一化值')
        self.video_feature_bar_canvas.axes.set_title('特征量化结果')
        self.video_feature_bar_canvas.axes.set_xticks(x)
        self.video_feature_bar_canvas.axes.set_xticklabels(features_names)
        self.video_feature_bar_canvas.axes.legend()
        
        self.video_feature_bar_canvas.fig.tight_layout()
        self.video_feature_bar_canvas.draw()
        
        # 更新饼图
        self.video_weight_pie_canvas.axes.clear()
        labels = '可见光', '红外'
        sizes = weights
        explode = (0.1, 0)  # 突出显示可见光部分
        
        self.video_weight_pie_canvas.axes.pie(sizes, explode=explode, labels=labels, autopct='%1.1f%%',
                shadow=True, startangle=90)
        self.video_weight_pie_canvas.axes.axis('equal')  # 保证饼图是圆的
        self.video_weight_pie_canvas.axes.set_title('模型分配权重')
        
        self.video_weight_pie_canvas.fig.tight_layout()
        self.video_weight_pie_canvas.draw()
    
    def closeEvent(self, event):
        """关闭窗口时释放资源"""
        if self.timer.isActive():
            self.timer.stop()
        if self.cap_visible is not None:
            self.cap_visible.release()
        if self.cap_ir is not None:
            self.cap_ir.release()
        event.accept()


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())    