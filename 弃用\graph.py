import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import models, transforms
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import dgl
import dgl.nn as dglnn
import cv2
from tqdm import tqdm
import glob

# 设置随机种子以确保结果可复现
torch.manual_seed(42)
np.random.seed(42)

class MultimodalDataset(Dataset):
    """多模态数据集：处理可见光和红外图像对"""
    def __init__(self, visible_dir, infrared_dir, labels=None, transform=None):
        self.visible_dir = visible_dir
        self.infrared_dir = infrared_dir
        self.transform = transform
        self.labels = labels
        
        # 获取图像文件列表
        self.visible_files = sorted(glob.glob(os.path.join(visible_dir, "*.jpg")))
        self.infrared_files = sorted(glob.glob(os.path.join(infrared_dir, "*.jpg")))
        
        # 确保可见光和红外图像数量匹配
        assert len(self.visible_files) == len(self.infrared_files), "可见光和红外图像数量不匹配"
        
        # 如果提供了标签，确保标签数量匹配
        if self.labels is not None:
            assert len(self.labels) == len(self.visible_files), "标签数量与图像数量不匹配"
    
    def __len__(self):
        return len(self.visible_files)
    
    def __getitem__(self, idx):
        # 读取图像
        visible_img = cv2.imread(self.visible_files[idx])
        visible_img = cv2.cvtColor(visible_img, cv2.COLOR_BGR2RGB)
        
        infrared_img = cv2.imread(self.infrared_files[idx])
        infrared_img = cv2.cvtColor(infrared_img, cv2.COLOR_BGR2RGB)
        
        # 应用图像变换
        if self.transform:
            visible_img = self.transform(visible_img)
            infrared_img = self.transform(infrared_img)
        
        # 获取标签（如果有）
        if self.labels is not None:
            label = self.labels[idx]
            return {'visible': visible_img, 'infrared': infrared_img, 'label': label}
        else:
            return {'visible': visible_img, 'infrared': infrared_img}

class MultiScaleFeatureExtractor(nn.Module):
    """多尺度特征提取网络"""
    def __init__(self, pretrained=True):
        super(MultiScaleFeatureExtractor, self).__init__()
        
        # 使用预训练的ResNet作为基础网络
        self.visible_backbone = models.resnet18(pretrained=pretrained)
        self.infrared_backbone = models.resnet18(pretrained=pretrained)
        
        # 移除最后的全连接层
        self.visible_features = nn.Sequential(*list(self.visible_backbone.children())[:-2])
        self.infrared_features = nn.Sequential(*list(self.infrared_backbone.children())[:-2])
        
        # 多尺度特征融合层
        self.conv1 = nn.Conv2d(512, 256, kernel_size=1)
        self.conv2 = nn.Conv2d(256, 128, kernel_size=1)
        self.pool = nn.AdaptiveAvgPool2d((1, 1))
        
    def forward(self, visible, infrared):
        # 提取多尺度特征
        v_feat = self.visible_features(visible)  # [batch_size, 512, 7, 7]
        i_feat = self.infrared_features(infrared)  # [batch_size, 512, 7, 7]
        
        # 特征融合
        feat = torch.cat([v_feat, i_feat], dim=1)  # [batch_size, 1024, 7, 7]
        
        # 多尺度特征提取
        scale1 = F.relu(self.conv1(feat))  # [batch_size, 256, 7, 7]
        scale2 = F.relu(self.conv2(scale1))  # [batch_size, 128, 7, 7]
        
        # 全局平均池化
        scale1_pooled = self.pool(scale1).squeeze(-1).squeeze(-1)  # [batch_size, 256]
        scale2_pooled = self.pool(scale2).squeeze(-1).squeeze(-1)  # [batch_size, 128]
        
        # 连接不同尺度的特征
        multi_scale_features = torch.cat([scale1_pooled, scale2_pooled], dim=1)  # [batch_size, 384]
        
        return {
            'scale1': scale1,
            'scale2': scale2,
            'global_features': multi_scale_features
        }

def create_graph_from_features(features, labels=None, k=5):
    """根据特征张量创建图结构"""
    # 计算样本间的相似度矩阵
    batch_size = features.shape[0]
    similarity = torch.matmul(features, features.transpose(1, 0))  # [batch_size, batch_size]
    
    # 创建图
    g = dgl.DGLGraph()
    g.add_nodes(batch_size)
    
    # 添加边（基于k近邻）
    for i in range(batch_size):
        # 获取与当前节点最相似的k个节点
        _, indices = torch.topk(similarity[i], k=k+1)  # +1因为包括自身
        indices = indices[1:]  # 排除自身
        
        # 添加边
        for j in indices:
            g.add_edge(i, j)
            g.add_edge(j, i)  # 确保图是无向的
    
    # 设置节点特征
    g.ndata['feat'] = features
    
    # 设置节点标签（如果提供）
    if labels is not None:
        g.ndata['label'] = labels
    
    return g

class GNNClassifier(nn.Module):
    """图神经网络分类器"""
    def __init__(self, in_feats, hidden_size, num_classes):
        super(GNNClassifier, self).__init__()
        self.conv1 = dglnn.GraphConv(in_feats, hidden_size)
        self.conv2 = dglnn.GraphConv(hidden_size, hidden_size)
        self.classifier = nn.Linear(hidden_size, num_classes)
        
    def forward(self, g):
        # GNN层
        h = self.conv1(g, g.ndata['feat'])
        h = F.relu(h)
        h = self.conv2(g, h)
        
        # 全局池化
        g.ndata['h'] = h
        hg = dgl.mean_nodes(g, 'h')
        
        # 分类器
        return self.classifier(hg)

def save_features_to_csv(features, image_paths, csv_path):
    """将特征保存到CSV文件"""
    # 转换为numpy数组
    if isinstance(features, torch.Tensor):
        features = features.cpu().detach().numpy()
    
    # 创建DataFrame
    columns = [f'feature_{i}' for i in range(features.shape[1])]
    df = pd.DataFrame(features, columns=columns)
    
    # 添加图像路径
    df['image_path'] = image_paths
    
    # 保存到CSV
    df.to_csv(csv_path, index=False)
    print(f"特征已保存到 {csv_path}")

def train_gnn_model(graph, num_classes, epochs=100, lr=0.001):
    """训练图神经网络模型"""
    # 创建模型
    model = GNNClassifier(
        in_feats=graph.ndata['feat'].shape[1],
        hidden_size=128,
        num_classes=num_classes
    )
    
    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)
    
    # 训练循环
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        
        # 前向传播
        logits = model(graph)
        loss = criterion(logits, graph.ndata['label'])
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 打印训练信息
        if (epoch + 1) % 10 == 0:
            pred = logits.argmax(1)
            acc = (pred == graph.ndata['label']).float().mean()
            print(f'Epoch {epoch+1}/{epochs}, Loss: {loss.item():.4f}, Accuracy: {acc.item():.4f}')
    
    return model

def evaluate_model(model, graph):
    """评估模型性能"""
    model.eval()
    with torch.no_grad():
        logits = model(graph)
        pred = logits.argmax(1).cpu().numpy()
        true_labels = graph.ndata['label'].cpu().numpy()
    
    # 打印分类报告
    print("\n分类报告:")
    print(classification_report(true_labels, pred))
    
    # 打印混淆矩阵
    print("\n混淆矩阵:")
    print(confusion_matrix(true_labels, pred))
    
    return pred, true_labels

def visualize_results(features, labels, predictions, title="特征可视化"):
    """可视化特征和分类结果"""
    from sklearn.manifold import TSNE
    import matplotlib.pyplot as plt
    
    # 使用t-SNE降维以便可视化
    tsne = TSNE(n_components=2, random_state=42)
    features_2d = tsne.fit_transform(features)
    
    # 绘制散点图
    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(features_2d[:, 0], features_2d[:, 1], c=predictions, 
                          cmap=plt.cm.get_cmap('viridis', len(np.unique(labels))),
                          s=50, alpha=0.8)
    
    # 添加颜色条和标题
    plt.colorbar(scatter, ticks=range(len(np.unique(labels))), 
                 label='Predicted Class')
    plt.title(title)
    plt.xlabel('t-SNE 1')
    plt.ylabel('t-SNE 2')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    plt.show()

def main():
    """主函数：运行完整的多模态图神经网络目标识别流程"""
    # 设置参数
    data_dir = "path/to/your/data"  # 数据目录，包含visible和infrared子目录
    visible_dir = os.path.join(data_dir, "visible")
    infrared_dir = os.path.join(data_dir, "infrared")
    csv_path = "features.csv"
    num_classes = 5  # 类别数量
    batch_size = 32
    epochs = 50
    
    # 数据预处理
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Resize((224, 224)),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 创建数据集和数据加载器
    print("加载数据...")
    dataset = MultimodalDataset(visible_dir, infrared_dir, transform=transform)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=False)
    
    # 初始化特征提取器
    print("初始化模型...")
    feature_extractor = MultiScaleFeatureExtractor(pretrained=True)
    feature_extractor.eval()  # 设置为评估模式
    
    # 提取特征
    print("提取特征...")
    all_features = []
    all_labels = []
    image_paths = []
    
    for batch in tqdm(dataloader):
        visible = batch['visible']
        infrared = batch['infrared']
        
        # 提取特征
        with torch.no_grad():
            features = feature_extractor(visible, infrared)
            global_features = features['global_features']
        
        all_features.append(global_features)
        image_paths.extend([dataset.visible_files[i] for i in range(len(visible))])
    
    # 合并所有特征
    all_features = torch.cat(all_features, dim=0)
    
    # 保存特征到CSV
    save_features_to_csv(all_features, image_paths, csv_path)
    
    # 创建图结构
    print("创建图结构...")
    # 假设我们有标签信息
    labels = torch.randint(0, num_classes, (len(all_features),))  # 示例标签，实际应用中应替换为真实标签
    graph = create_graph_from_features(all_features, labels)
    
    # 训练GNN模型
    print("训练GNN模型...")
    model = train_gnn_model(graph, num_classes, epochs=epochs)
    
    # 评估模型
    print("评估模型...")
    predictions, true_labels = evaluate_model(model, graph)
    
    # 可视化结果
    visualize_results(all_features.numpy(), true_labels, predictions)
    
    print("流程完成!")

if __name__ == "__main__":
    main()    