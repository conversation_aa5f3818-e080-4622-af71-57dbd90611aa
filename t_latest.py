import sys
import os
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QFileDialog, QSlider, 
                            QSplitter, QMessageBox, QGridLayout, QComboBox, QGroupBox)
from PyQt5.QtGui import QPixmap, QImage, QFont
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import time
import csv
from datetime import datetime

# 设置matplotlib中文字体
plt.rcParams["font.family"] = ["SimHei"]
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class FeatureExtractor:
    """特征提取器，用于从图像中提取各种特征"""
    
    @staticmethod
    def calculate_entropy(image):
        """计算图像的信息熵"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        hist = cv2.calcHist([image], [0], None, [256], [0, 256])
        hist = hist.ravel() / hist.sum()
        entropy = -np.sum(hist * np.log2(hist + np.finfo(float).eps))
        return entropy
    
    @staticmethod
    def calculate_contrast(image):
        """计算图像的对比度"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        mean = np.mean(image)
        contrast = np.sqrt(np.mean((image - mean) ** 2))
        return contrast
    
    @staticmethod
    def calculate_energy(image):
        """计算图像的能量"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 使用拉普拉斯算子检测边缘作为能量指标
        laplacian = cv2.Laplacian(image, cv2.CV_64F)
        energy = np.sum(laplacian ** 2)
        energy /= (image.shape[0] * image.shape[1])
        return energy
    
    @staticmethod
    def calculate_structure(image):
        """计算图像的结构复杂度"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 使用Canny边缘检测
        edges = cv2.Canny(image, 100, 200)
        structure = np.sum(edges) / (edges.shape[0] * edges.shape[1])
        return structure
    
    @staticmethod
    def calculate_high_frequency(image):
        """计算图像的高频复杂度"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 使用傅里叶变换
        f = np.fft.fft2(image)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift))
        
        # 计算高频部分的能量
        rows, cols = image.shape
        crow, ccol = rows // 2, cols // 2
        high_freq = magnitude_spectrum[crow-30:crow+30, ccol-30:ccol+30]
        high_freq_energy = np.sum(high_freq) / (high_freq.size)
        return high_freq_energy
    
    @staticmethod
    def calculate_mean_gray(image):
        """计算图像的平均灰度值"""
        if len(image.shape) > 2:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return np.mean(image)
    
    @staticmethod
    def extract_edge(image):
        """提取图像边缘"""
        if len(image.shape) > 2:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        edges = cv2.Canny(gray, 50, 200)
        return edges
    
    @staticmethod
    def extract_corners(image):
        """提取图像角点"""
        if len(image.shape) > 2:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        gray = np.float32(gray)
        dst = cv2.cornerHarris(gray, 2, 3, 0.04)
        dst = cv2.dilate(dst, None)
        return dst
    
    @staticmethod
    def extract_frequency(image):
        """提取图像频率特征"""
        if len(image.shape) > 2:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        f = np.fft.fft2(gray)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift))
        return magnitude_spectrum
    
    @staticmethod
    def extract_gray_curve(image):
        """提取灰度曲线"""
        if len(image.shape) > 2:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        return hist


class FeatureVisualizer(QWidget):
    """特征可视化窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
        
    def initUI(self):
        self.setWindowTitle('特征提取结果')
        self.setGeometry(100, 100, 1000, 800)
        
        self.main_layout = QGridLayout(self)
        
        # 创建matplotlib图表
        self.figures = []
        self.canvases = []
        
        # 创建4x2的网格布局
        for i in range(4):
            for j in range(2):
                fig = Figure(figsize=(5, 4), dpi=100)
                canvas = FigureCanvas(fig)
                self.figures.append(fig)
                self.canvases.append(canvas)
                self.main_layout.addWidget(canvas, i, j)
        
        self.setLayout(self.main_layout)
    
    def update_features(self, visible_image, ir_image):
        """更新特征可视化"""
        # 确保图像不为空
        if visible_image is None or ir_image is None:
            return
            
        # 清空所有图表
        for fig in self.figures:
            fig.clear()
        
        # 1. 原始可见光图像
        ax1 = self.figures[0].add_subplot(111)
        ax1.imshow(cv2.cvtColor(visible_image, cv2.COLOR_BGR2RGB))
        ax1.set_title('可见光原始图像')
        ax1.axis('off')
        
        # 2. 原始红外图像
        ax2 = self.figures[1].add_subplot(111)
        ax2.imshow(cv2.cvtColor(ir_image, cv2.COLOR_BGR2RGB))
        ax2.set_title('红外原始图像')
        ax2.axis('off')
        
        # 3. 可见光边缘
        ax3 = self.figures[2].add_subplot(111)
        edges_vis = FeatureExtractor.extract_edge(visible_image)
        ax3.imshow(edges_vis, cmap='gray')
        ax3.set_title('可见光边缘')
        ax3.axis('off')
        
        # 4. 红外边缘
        ax4 = self.figures[3].add_subplot(111)
        edges_ir = FeatureExtractor.extract_edge(ir_image)
        ax4.imshow(edges_ir, cmap='gray')
        ax4.set_title('红外边缘')
        ax4.axis('off')
        
        # 5. 可见光角点
        ax5 = self.figures[4].add_subplot(111)
        corners_vis = FeatureExtractor.extract_corners(visible_image)
        visible_corners = visible_image.copy()
        visible_corners[corners_vis > 0.01 * corners_vis.max()] = [0, 0, 255]
        ax5.imshow(cv2.cvtColor(visible_corners, cv2.COLOR_BGR2RGB))
        ax5.set_title('可见光角点')
        ax5.axis('off')
        
        # 6. 红外角点
        ax6 = self.figures[5].add_subplot(111)
        corners_ir = FeatureExtractor.extract_corners(ir_image)
        ir_corners = ir_image.copy()
        ir_corners[corners_ir > 0.01 * corners_ir.max()] = [0, 0, 255]
        ax6.imshow(cv2.cvtColor(ir_corners, cv2.COLOR_BGR2RGB))
        ax6.set_title('红外角点')
        ax6.axis('off')
        
        # 7. 可见光频率特征
        ax7 = self.figures[6].add_subplot(111)
        freq_vis = FeatureExtractor.extract_frequency(visible_image)
        ax7.imshow(freq_vis, cmap='gray')
        ax7.set_title('可见光频率特征')
        ax7.axis('off')
        
        # 8. 红外频率特征
        ax8 = self.figures[7].add_subplot(111)
        freq_ir = FeatureExtractor.extract_frequency(ir_image)
        ax8.imshow(freq_ir, cmap='gray')
        ax8.set_title('红外频率特征')
        ax8.axis('off')
        
        # 更新画布
        for canvas in self.canvases:
            canvas.draw()


class WeightCalculator:
    """权重计算器，用于计算不同探测体制的权重"""
    
    def __init__(self):
        # 初始化特征权重（可根据需求调整）
        self.feature_weights = {
            'entropy': 0.15,       # 信息熵权重
            'contrast': 0.15,      # 对比度权重
            'energy': 0.15,        # 能量权重
            'structure': 0.15,     # 结构复杂度权重
            'high_frequency': 0.15, # 高频复杂度权重
            'gray': 0.25           # 灰度权重
        }
        
        # 白天场景下的特征权重调整系数
        self.daytime_weight_factors = {
            'visible': {
                'entropy': 1.2,
                'contrast': 1.3,
                'energy': 1.1,
                'structure': 1.2,
                'high_frequency': 1.3,
                'gray': 1.0
            },
            'ir': {
                'entropy': 0.8,
                'contrast': 0.7,
                'energy': 0.9,
                'structure': 0.8,
                'high_frequency': 0.7,
                'gray': 0.9
            }
        }
        
        # 夜晚场景下的特征权重调整系数
        self.nighttime_weight_factors = {
            'visible': {
                'entropy': 0.7,
                'contrast': 0.6,
                'energy': 0.8,
                'structure': 0.7,
                'high_frequency': 0.6,
                'gray': 0.5
            },
            'ir': {
                'entropy': 1.3,
                'contrast': 1.4,
                'energy': 1.2,
                'structure': 1.3,
                'high_frequency': 1.4,
                'gray': 1.5
            }
        }
    
    def calculate_weights(self, features_visible, features_ir):
        """计算可见光和红外的总权重"""
        # 判断场景是白天还是夜晚
        is_daytime = self._determine_scene(features_visible, features_ir)
        
        # 根据场景选择权重调整系数
        if is_daytime:
            weight_factors = self.daytime_weight_factors
            scene = "白天"
        else:
            weight_factors = self.nighttime_weight_factors
            scene = "夜晚"
        
        # 计算可见光和红外的加权分数
        score_visible = 0
        score_ir = 0
        
        for feature, weight in self.feature_weights.items():
            score_visible += features_visible[feature] * weight * weight_factors['visible'][feature]
            score_ir += features_ir[feature] * weight * weight_factors['ir'][feature]
        
        # 归一化权重
        total_score = score_visible + score_ir
        if total_score == 0:
            weight_visible = 0.5
            weight_ir = 0.5
        else:
            weight_visible = score_visible / total_score
            weight_ir = score_ir / total_score
        
        return {
            'weight_visible': weight_visible,
            'weight_ir': weight_ir,
            'is_daytime': is_daytime,
            'scene': scene
        }
    
    def _determine_scene(self, features_visible, features_ir):
        """判断场景是白天还是夜晚"""
        # 基于灰度值、对比度和高频复杂度判断
        visible_gray = features_visible['gray']
        ir_gray = features_ir['gray']
        visible_contrast = features_visible['contrast']
        ir_contrast = features_ir['contrast']
        visible_high_freq = features_visible['high_frequency']
        ir_high_freq = features_ir['high_frequency']
        
        # 白天场景特征：可见光图像灰度值高、对比度高、高频复杂度高
        # 夜晚场景特征：红外图像灰度值高、对比度高、高频复杂度高
        
        # 计算白天指数（值越高越可能是白天）
        day_index = (visible_gray / max(ir_gray, 1)) * 0.4 + \
                   (visible_contrast / max(ir_contrast, 1)) * 0.3 + \
                   (visible_high_freq / max(ir_high_freq, 1)) * 0.3
        
        return day_index > 1.2  # 阈值可调整


class VideoProcessor(QThread):
    """视频处理线程"""
    
    frame_processed = pyqtSignal(dict)
    
    def __init__(self, visible_path, ir_path, parent=None):
        super().__init__(parent)
        self.visible_path = visible_path
        self.ir_path = ir_path
        self.is_running = True
        self.weight_calculator = WeightCalculator()
        self.frame_count = 0
        self.feature_extractor = FeatureExtractor()
        self.features_history = []
    
    def run(self):
        # 打开视频文件或摄像头
        if self.visible_path.lower().startswith('cam'):
            cam_id = int(self.visible_path.split(':')[1]) if len(self.visible_path.split(':')) > 1 else 0
            visible_cap = cv2.VideoCapture(cam_id)
        else:
            visible_cap = cv2.VideoCapture(self.visible_path)
        
        if self.ir_path.lower().startswith('cam'):
            cam_id = int(self.ir_path.split(':')[1]) if len(self.ir_path.split(':')) > 1 else 1
            ir_cap = cv2.VideoCapture(cam_id)
        else:
            ir_cap = cv2.VideoCapture(self.ir_path)
        
        # 检查视频是否成功打开
        if not visible_cap.isOpened() or not ir_cap.isOpened():
            self.frame_processed.emit({
                'error': '无法打开视频文件或摄像头'
            })
            return
        
        while self.is_running:
            ret_vis, visible_frame = visible_cap.read()
            ret_ir, ir_frame = ir_cap.read()
            
            # 如果任一视频结束，则停止处理
            if not ret_vis or not ret_ir:
                break
            
            # 调整图像大小以匹配
            if visible_frame.shape != ir_frame.shape:
                ir_frame = cv2.resize(ir_frame, (visible_frame.shape[1], visible_frame.shape[0]))
            
            # 提取特征
            features_visible = self._extract_all_features(visible_frame)
            features_ir = self._extract_all_features(ir_frame)
            
            # 计算权重
            weights = self.weight_calculator.calculate_weights(features_visible, features_ir)
            
            # 保存特征历史记录
            self.features_history.append({
                'timestamp': time.time(),
                'features_visible': features_visible,
                'features_ir': features_ir,
                'weights': weights
            })
            
            # 发送处理结果
            self.frame_processed.emit({
                'visible_frame': visible_frame,
                'ir_frame': ir_frame,
                'features_visible': features_visible,
                'features_ir': features_ir,
                'weights': weights,
                'frame_count': self.frame_count
            })
            
            self.frame_count += 1
            
            # 控制处理速度
            self.msleep(30)
        
        # 释放资源
        visible_cap.release()
        ir_cap.release()
    
    def stop(self):
        """停止视频处理线程"""
        self.is_running = False
        self.wait()
    
    def _extract_all_features(self, image):
        """提取图像的所有特征"""
        return {
            'entropy': self.feature_extractor.calculate_entropy(image),
            'contrast': self.feature_extractor.calculate_contrast(image),
            'energy': self.feature_extractor.calculate_energy(image),
            'structure': self.feature_extractor.calculate_structure(image),
            'high_frequency': self.feature_extractor.calculate_high_frequency(image),
            'gray': self.feature_extractor.calculate_mean_gray(image)
        }
    
    def save_features_to_csv(self, file_path):
        """将特征历史记录保存到CSV文件"""
        if not self.features_history:
            return False
        
        # 准备CSV文件的表头
        header = ['timestamp', 'scene']
        for feature in self.features_history[0]['features_visible'].keys():
            header.append(f'visible_{feature}')
            header.append(f'ir_{feature}')
        header.extend(['weight_visible', 'weight_ir'])
        
        # 写入CSV文件
        try:
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=header)
                writer.writeheader()
                
                for record in self.features_history:
                    row = {
                        'timestamp': datetime.fromtimestamp(record['timestamp']).strftime('%Y-%m-%d %H:%M:%S'),
                        'scene': '白天' if record['weights']['is_daytime'] else '夜晚',
                        'weight_visible': record['weights']['weight_visible'],
                        'weight_ir': record['weights']['weight_ir']
                    }
                    
                    for feature, value in record['features_visible'].items():
                        row[f'visible_{feature}'] = value
                    
                    for feature, value in record['features_ir'].items():
                        row[f'ir_{feature}'] = value
                    
                    writer.writerow(row)
            
            return True
        except Exception as e:
            print(f"保存CSV文件时出错: {e}")
            return False


class FeaturePlotWidget(QWidget):
    """特征绘图部件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
    
    def initUI(self):
        layout = QVBoxLayout(self)
        
        # 创建柱状图和饼图的画布
        self.fig_bar = Figure(figsize=(8, 4), dpi=100)
        self.canvas_bar = FigureCanvas(self.fig_bar)
        
        self.fig_pie = Figure(figsize=(4, 4), dpi=100)
        self.canvas_pie = FigureCanvas(self.fig_pie)
        
        # 创建场景标签
        self.scene_label = QLabel("场景: 未知")
        self.scene_label.setFont(QFont("SimHei", 12, QFont.Bold))
        self.scene_label.setAlignment(Qt.AlignCenter)
        
        # 水平布局放置柱状图和饼图
        plot_layout = QHBoxLayout()
        plot_layout.addWidget(self.canvas_bar)
        plot_layout.addWidget(self.canvas_pie)
        
        layout.addLayout(plot_layout)
        layout.addWidget(self.scene_label)
        
        self.setLayout(layout)
    
    def update_plots(self, features_visible, features_ir, weights):
        """更新图表"""
        # 清空图表
        self.fig_bar.clear()
        self.fig_pie.clear()
        
        # 绘制特征对比柱状图
        ax1 = self.fig_bar.add_subplot(111)
        features = list(features_visible.keys())
        x = np.arange(len(features))
        width = 0.35
        
        visible_values = [features_visible[feature] for feature in features]
        ir_values = [features_ir[feature] for feature in features]
        
        rects1 = ax1.bar(x - width/2, visible_values, width, label='可见光')
        rects2 = ax1.bar(x + width/2, ir_values, width, label='红外')
        
        ax1.set_ylabel('特征值')
        ax1.set_title('特征对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(features, rotation=45)
        ax1.legend()
        
        # 添加数值标签
        def autolabel(rects):
            for rect in rects:
                height = rect.get_height()
                ax1.annotate('{0:.2f}'.format(height),
                            xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
        
        autolabel(rects1)
        autolabel(rects2)
        
        self.fig_bar.tight_layout()
        
        # 绘制权重饼图
        ax2 = self.fig_pie.add_subplot(111)
        labels = '可见光', '红外'
        sizes = [weights['weight_visible'], weights['weight_ir']]
        colors = ['lightskyblue', 'lightcoral']
        explode = (0.1, 0)  # 突出显示可见光部分
        
        ax2.pie(sizes, explode=explode, labels=labels, colors=colors,
                autopct='%1.1f%%', shadow=True, startangle=90)
        ax2.axis('equal')  # 保证饼图是圆的
        ax2.set_title('探测体制权重分配')
        
        self.fig_pie.tight_layout()
        
        # 更新场景标签
        self.scene_label.setText(f"场景: {weights['scene']}")
        
        # 刷新画布
        self.canvas_bar.draw()
        self.canvas_pie.draw()


class FeatureTableWidget(QWidget):
    """特征表格部件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
    
    def initUI(self):
        layout = QVBoxLayout(self)
        
        # 创建特征显示标签
        self.feature_labels = {}
        
        features = ['信息熵', '对比度', '能量', '结构复杂度', '高频复杂度', '平均灰度']
        detectors = ['可见光', '红外']
        
        # 创建网格布局
        grid = QGridLayout()
        
        # 添加表头
        grid.addWidget(QLabel("特征"), 0, 0)
        for i, detector in enumerate(detectors):
            grid.addWidget(QLabel(detector), 0, i+1)
        
        # 添加特征行
        for row, feature in enumerate(features, 1):
            grid.addWidget(QLabel(feature), row, 0)
            for col, detector in enumerate(detectors, 1):
                label = QLabel("0.00")
                label.setAlignment(Qt.AlignCenter)
                grid.addWidget(label, row, col)
                self.feature_labels[f"{detector}_{feature}"] = label
        
        # 添加权重行
        grid.addWidget(QLabel("最终权重"), len(features)+1, 0)
        for col, detector in enumerate(detectors, 1):
            label = QLabel("0.00")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("font-weight: bold; color: blue;")
            grid.addWidget(label, len(features)+1, col)
            self.feature_labels[f"{detector}_权重"] = label
        
        layout.addLayout(grid)
        self.setLayout(layout)
    
    def update_features(self, features_visible, features_ir, weights):
        """更新特征显示"""
        # 映射特征名称
        feature_map = {
            'entropy': '信息熵',
            'contrast': '对比度',
            'energy': '能量',
            'structure': '结构复杂度',
            'high_frequency': '高频复杂度',
            'gray': '平均灰度'
        }
        
        # 更新可见光特征
        for feature_key, feature_name in feature_map.items():
            self.feature_labels[f"可见光_{feature_name}"].setText(f"{features_visible[feature_key]:.4f}")
        
        # 更新红外特征
        for feature_key, feature_name in feature_map.items():
            self.feature_labels[f"红外_{feature_name}"].setText(f"{features_ir[feature_key]:.4f}")
        
        # 更新权重
        self.feature_labels["可见光_权重"].setText(f"{weights['weight_visible']:.4f}")
        self.feature_labels["红外_权重"].setText(f"{weights['weight_ir']:.4f}")


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.initUI()
        self.video_processor = None
        self.feature_visualizer = None
        self.frame_counter = 0
    
    def initUI(self):
        self.setWindowTitle('多模态图像特征分析系统')
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部控制区域
        control_layout = QHBoxLayout()
        
        # 创建文件选择按钮
        self.visible_file_btn = QPushButton('选择可见光图像/视频')
        self.visible_file_btn.clicked.connect(self.select_visible_file)
        self.ir_file_btn = QPushButton('选择红外图像/视频')
        self.ir_file_btn.clicked.connect(self.select_ir_file)
        
        # 创建启动和停止按钮
        self.start_btn = QPushButton('开始处理')
        self.start_btn.clicked.connect(self.start_processing)
        self.stop_btn = QPushButton('停止处理')
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        
        # 创建保存CSV按钮
        self.save_csv_btn = QPushButton('保存特征到CSV')
        self.save_csv_btn.clicked.connect(self.save_to_csv)
        self.save_csv_btn.setEnabled(False)
        
        # 添加到控制布局
        control_layout.addWidget(self.visible_file_btn)
        control_layout.addWidget(self.ir_file_btn)
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.save_csv_btn)
        
        # 创建图像显示区域
        image_layout = QHBoxLayout()
        
        # 可见光图像显示
        self.visible_label = QLabel('可见光图像')
        self.visible_label.setAlignment(Qt.AlignCenter)
        self.visible_label.setMinimumSize(400, 300)
        self.visible_label.setStyleSheet("border: 1px solid #cccccc;")
        
        # 红外图像显示
        self.ir_label = QLabel('红外图像')
        self.ir_label.setAlignment(Qt.AlignCenter)
        self.ir_label.setMinimumSize(400, 300)
        self.ir_label.setStyleSheet("border: 1px solid #cccccc;")
        
        image_layout.addWidget(self.visible_label)
        image_layout.addWidget(self.ir_label)
        
        # 创建特征显示区域
        feature_layout = QHBoxLayout()
        
        # 特征表格
        self.feature_table = FeatureTableWidget()
        
        # 特征图表
        self.feature_plot = FeaturePlotWidget()
        
        feature_layout.addWidget(self.feature_table)
        feature_layout.addWidget(self.feature_plot)
        
        # 添加所有布局到主布局
        main_layout.addLayout(control_layout)
        main_layout.addLayout(image_layout)
        main_layout.addLayout(feature_layout)
        
        # 初始化文件路径
        self.visible_path = None
        self.ir_path = None
    
    def select_visible_file(self):
        """选择可见光图像/视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择可见光图像/视频", "", 
            "图像文件 (*.png *.jpg *.jpeg *.bmp);;视频文件 (*.mp4 *.avi *.mov);;摄像头 (摄像头0: cam:0; 摄像头1: cam:1)"
        )
        
        if file_path:
            self.visible_path = file_path
            self.visible_file_btn.setText(f"可见光: {os.path.basename(file_path)}")
            
            # 如果是图像文件，直接显示
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                self.display_image(self.visible_label, file_path)
    
    def select_ir_file(self):
        """选择红外图像/视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择红外图像/视频", "", 
            "图像文件 (*.png *.jpg *.jpeg *.bmp);;视频文件 (*.mp4 *.avi *.mov);;摄像头 (摄像头0: cam:0; 摄像头1: cam:1)"
        )
        
        if file_path:
            self.ir_path = file_path
            self.ir_file_btn.setText(f"红外: {os.path.basename(file_path)}")
            
            # 如果是图像文件，直接显示
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                self.display_image(self.ir_label, file_path)
    
    def display_image(self, label, image_path):
        """显示图像"""
        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            label.setPixmap(scaled_pixmap)
    
    def start_processing(self):
        """开始处理图像/视频"""
        if not self.visible_path or not self.ir_path:
            QMessageBox.warning(self, "警告", "请先选择可见光和红外图像/视频文件")
            return
        
        # 禁用开始按钮，启用停止按钮
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.save_csv_btn.setEnabled(False)
        
        # 创建并启动视频处理线程
        self.video_processor = VideoProcessor(self.visible_path, self.ir_path)
        self.video_processor.frame_processed.connect(self.update_display)
        self.video_processor.start()
        
        # 重置帧计数器
        self.frame_counter = 0
    
    def stop_processing(self):
        """停止处理"""
        if self.video_processor and self.video_processor.isRunning():
            self.video_processor.stop()
        
        # 启用开始按钮，禁用停止按钮
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.save_csv_btn.setEnabled(True)
    
    def update_display(self, results):
        """更新显示"""
        if 'error' in results:
            QMessageBox.critical(self, "错误", results['error'])
            self.stop_processing()
            return
        
        # 更新图像显示
        visible_frame = results['visible_frame']
        ir_frame = results['ir_frame']
        
        self.display_cv_image(self.visible_label, visible_frame)
        self.display_cv_image(self.ir_label, ir_frame)
        
        # 更新特征显示
        self.feature_table.update_features(results['features_visible'], results['features_ir'], results['weights'])
        self.feature_plot.update_plots(results['features_visible'], results['features_ir'], results['weights'])
        
        # 每10帧显示一次特征提取结果
        self.frame_counter += 1
        if self.frame_counter % 10 == 0:
            if not self.feature_visualizer:
                self.feature_visualizer = FeatureVisualizer()
            
            self.feature_visualizer.update_features(visible_frame, ir_frame)
            self.feature_visualizer.show()
    
    def display_cv_image(self, label, cv_image):
        """将OpenCV图像显示在QLabel上"""
        rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        convert_to_Qt_format = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        p = convert_to_Qt_format.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        label.setPixmap(QPixmap.fromImage(p))
    
    def save_to_csv(self):
        """保存特征到CSV文件"""
        if not self.video_processor or not self.video_processor.features_history:
            QMessageBox.warning(self, "警告", "没有特征数据可供保存")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存特征数据", "", "CSV文件 (*.csv)"
        )
        
        if file_path:
            if self.video_processor.save_features_to_csv(file_path):
                QMessageBox.information(self, "成功", f"特征数据已成功保存到 {file_path}")
            else:
                QMessageBox.critical(self, "错误", "保存特征数据时出错")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())    