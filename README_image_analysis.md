# 分析系统

一个完整的Python图像分析系统，支持场景识别、特征提取、几何特征分析、部件查询和可视化功能。

## 功能特性

### 1. 场景识别和特征提取
- 提取图像的基本统计特征（均值、标准差、熵、对比度等）
- 计算边缘密度和结构复杂度
- 特征归一化处理
- 与预定义场景模板进行对比匹配

### 2. 几何特征分析
- **长宽比计算**：分析目标个体的形状特征
- **圆度计算**：评估目标的圆形程度
- **高热区域定位**：找到图像中最亮区域的相对位置
- **背景对比度**：计算目标与背景的灰度差异

### 3. 部件查询和关系分析
- 通过CSV文件查询部件是否属于指定个体类别
- 分析部件间的相对位置关系（前、后、左、右等）
- 自动生成相关部件的预测边界框

### 4. 可视化功能
- 在图像上标注个体、部件和相关部件的边界框
- 显示分析结果和特征信息
- 生成综合分析报告图表

## 文件结构

```
├── image_analysis_system.py    # 主系统类
├── main_demo.py               # 演示程序
├── advanced_test.py           # 高级测试脚本
├── config.py                  # 配置文件
├── benti/diankeyihao.csv     # 部件关系数据
└── README_image_analysis.md   # 说明文档
```

## 安装依赖

```bash
pip install opencv-python numpy pandas matplotlib scikit-learn
```

## 快速开始

### 1. 基本使用

```python
from image_analysis_system import ImageAnalysisSystem

# 初始化系统
system = ImageAnalysisSystem()

# 处理图像
results = system.process_image(
    image_path="your_image.jpg",
    scene_description="晴天",
    individual_bbox=(100, 100, 200, 150),  # (x, y, width, height)
    component_bbox=(150, 125, 80, 60),
    entity_type="电科一号",
    component_name="雷达"
)

# 查看结果
print("场景匹配度:", results['scene_comparison']['match_score'])
print("几何特征:", results['geometric_features'])
```

### 2. 运行演示程序

```bash
python main_demo.py
```

### 3. 运行高级测试

```bash
python advanced_test.py
```

## 配置说明

### 场景模板配置

在 `config.py` 中可以配置不同场景的特征期望值：

```python
SCENE_TEMPLATES = {
    '晴天': {
        'mean_intensity': (120, 200),
        'contrast': (20, 50),
        'entropy': (4.0, 6.0),
        'edge_density': (0.01, 0.05)
    },
    # 其他场景...
}
```

### 可视化配置

```python
VISUALIZATION_CONFIG = {
    'colors': {
        'individual': (0, 255, 0),    # 绿色 - 个体
        'component': (255, 0, 0),     # 红色 - 部件
        'related': (0, 0, 255)        # 蓝色 - 相关部件
    }
}
```

## API 参考

### ImageAnalysisSystem 类

#### 主要方法

- `extract_image_features(image)`: 提取图像特征
- `normalize_features(features)`: 归一化特征
- `compare_with_scene(features, scene_description)`: 场景对比
- `extract_geometric_features(image, bbox)`: 提取几何特征
- `query_component_relations(component_name, entity_type)`: 查询部件关系
- `visualize_results(...)`: 可视化结果
- `process_image(...)`: 完整处理流程

#### 返回结果格式

```python
{
    'image_features': {...},           # 图像特征
    'scene_comparison': {...},         # 场景对比结果
    'geometric_features': {...},       # 几何特征
    'component_relations': {...},      # 部件关系
    'related_bboxes': [...],          # 相关边界框
    'result_image': np.ndarray        # 结果图像
}
```

## 使用示例

### 示例1：分析单张图像

```python
import cv2
from image_analysis_system import ImageAnalysisSystem

# 初始化系统
system = ImageAnalysisSystem("benti/diankeyihao.csv")

# 处理图像
results = system.process_image(
    image_path="test_image.jpg",
    scene_description="晴天",
    individual_bbox=(100, 100, 200, 150),
    component_bbox=(150, 125, 80, 60)
)

# 保存结果
cv2.imwrite("analysis_result.jpg", results['result_image'])
```

### 示例2：批量处理

```python
import os
from image_analysis_system import ImageAnalysisSystem

system = ImageAnalysisSystem()
image_dir = "test_images"

for filename in os.listdir(image_dir):
    if filename.endswith(('.jpg', '.png')):
        image_path = os.path.join(image_dir, filename)
        
        results = system.process_image(
            image_path=image_path,
            scene_description="晴天",
            individual_bbox=(100, 100, 200, 150),
            component_bbox=(150, 125, 80, 60)
        )
        
        # 保存结果
        output_path = f"result_{filename}"
        cv2.imwrite(output_path, results['result_image'])
```

## 测试

系统包含多种测试：

1. **基本功能测试**：测试各个模块的基本功能
2. **场景测试**：测试不同天气场景的识别
3. **边界情况测试**：测试极端情况下的系统稳定性
4. **性能测试**：测试不同图像尺寸下的处理速度

运行测试：
```bash
python advanced_test.py
```

## 注意事项

1. 确保CSV文件格式正确，包含必要的列：Head Entity, Relation, Tail Entity
2. 边界框坐标格式为 (x, y, width, height)
3. 图像路径必须存在且格式支持
4. 建议图像尺寸不超过1920x1080以保证处理速度

## 扩展功能

系统设计为模块化，可以轻松扩展：

1. 添加新的场景类型
2. 扩展几何特征计算
3. 增加新的可视化样式
4. 集成深度学习模型

## 故障排除

### 常见问题

1. **ImportError**: 确保安装了所有依赖包
2. **FileNotFoundError**: 检查图像路径和CSV文件路径
3. **ValueError**: 检查边界框坐标是否在图像范围内
4. **内存不足**: 对大图像进行预处理缩放

### 调试模式

在 `config.py` 中启用调试模式：
```python
SYSTEM_CONFIG = {
    'debug_mode': True,
    'save_intermediate_results': True
}
```

## 许可证

本项目采用MIT许可证。
