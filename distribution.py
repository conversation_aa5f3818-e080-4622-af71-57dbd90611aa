import cv2
import numpy as np
from skimage.feature import hog
from skimage.measure import shannon_entropy
import matplotlib.pyplot as plt
def load_infrared_image(image_path):
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if image is None:
        raise ValueError("Image could not be read")
    return image

def detect_target_region(image):
    # Use Canny edge detection to find edges in the image
    edges = cv2.Canny(image, 50, 150)
    
    # Find contours in the edge-detected image
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Assuming the largest contour is the target region
    if not contours:
        raise ValueError("No contours found in the image")
    
    max_contour = max(contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(max_contour)
    
    # Extract the target region
    target_region = image[y:y+h, x:x+w]
    return target_region, (x, y, w, h)

def extract_hog_features(target_region):
    hog_features, hog_image = hog(target_region, orientations=8, pixels_per_cell=(16, 16),
                                   cells_per_block=(1, 1), block_norm='L2-Hys', visualize=True)
    return hog_features, hog_image

def calculate_entropy(image):
    entropy = shannon_entropy(image.ravel())
    return entropy

def analyze_anisotropy(hog_features, entropy):
    # Example analysis: Sum of absolute differences between adjacent orientation bins
    anisotropy_score = np.sum(np.abs(np.diff(hog_features)))
    return anisotropy_score, entropy

def display_results(original_image, target_region, hog_image, anisotropy_score, entropy):
    # Draw bounding box around the target region on the original image
    x, y, w, h = cv2.boundingRect(cv2.findContours(cv2.Canny(target_region, 50, 150), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)[0][0])
    cv2.rectangle(original_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
    
    # Display images
    # cv2.imshow('Original Image', original_image)
    # cv2.imshow('Target Region', target_region)
    # cv2.imshow('HOG Visualization', hog_image)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()
    plt.figure(figsize=(12, 6))
    plt.subplot(1, 3, 1)
    plt.imshow(original_image, cmap='gray')
    plt.title('Original Image with Target Region')
    plt.subplot(1, 3, 2)
    plt.imshow(target_region, cmap='gray')
    plt.title('Target Region')
    plt.subplot(1, 3, 3)
    plt.imshow(hog_image, cmap='gray')
    plt.title('HOG Features')
    plt.tight_layout()
    plt.show()
    
    print(f"Anisotropy Score: {anisotropy_score}")
    print(f"Entropy: {entropy}")

def main():
    infrared_image_path = "infrared/000000.jpg"
    infrared_image = load_infrared_image(infrared_image_path)
    
    # Detect target region
    target_region, bbox = detect_target_region(infrared_image)
    
    # Extract HOG features
    hog_features, hog_image = extract_hog_features(target_region)
    
    # Calculate entropy
    entropy = calculate_entropy(target_region)
    
    # Analyze anisotropy
    anisotropy_score, entropy = analyze_anisotropy(hog_features, entropy)
    
    # Display results
    display_results(infrared_image.copy(), target_region, hog_image, anisotropy_score, entropy)

if __name__ == "__main__":
    main()