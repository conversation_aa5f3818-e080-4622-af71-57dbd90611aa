{"test_summary": {"total_tests": 3, "successful_tests": 3, "failed_tests": 0, "timestamp": "2025-07-07T17:05:00.848841"}, "test_results": [{"scenario_name": "晴天场景测试", "timestamp": "2025-07-07T17:05:00.808684", "scene_match_score": 0.7856004106920315, "scene_is_match": true, "geometric_features": {"aspect_ratio": 1.3333333333333333, "circularity": 0.7691848640143262, "hot_region_position": [0.425, 0.7066666666666667], "background_contrast": 52.51898730158729}, "component_belongs": true, "related_components_count": 1, "success": true}, {"scenario_name": "雨天场景测试", "timestamp": "2025-07-07T17:05:00.824008", "scene_match_score": 0.75, "scene_is_match": true, "geometric_features": {"aspect_ratio": 1.2857142857142858, "circularity": 0.10323884565193284, "hot_region_position": [0.022222222222222223, 0.7571428571428571], "background_contrast": 18.073152313407633}, "component_belongs": true, "related_components_count": 1, "success": true}, {"scenario_name": "夜晚场景测试", "timestamp": "2025-07-07T17:05:00.846840", "scene_match_score": 0.75, "scene_is_match": true, "geometric_features": {"aspect_ratio": 1.375, "circularity": 0.7794986432037061, "hot_region_position": [0.02727272727272727, 0.00625], "background_contrast": 84.52145922459893}, "component_belongs": true, "related_components_count": 1, "success": true}]}