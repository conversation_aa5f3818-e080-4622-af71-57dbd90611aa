"""
作者: stark
日期: 2024年12月05日
"""

from ultralytics import YOL<PERSON>

if __name__ == "__main__":
    # ====== 配置任务类型和运行模式 ======
    task = 'detect'  # 任务类型，可选：'detect', 'segment', 'classify', 'pose'
    mode = 'train'   # 运行模式，可选：'train', 'val', 'predict', 'export', 'track', 'benchmark'

    # ====== 初始化模型 ======
    if task == 'detect':
        model = YOLO(r'yolo11s.pt')  # (str, optional) 使用目标检测模型， path to model file, i.e. yolov8n.pt, yolov8n.yaml
    elif task == 'segment':
        model = YOLO('yolov8n-seg.pt')  # 使用实例分割模型
    elif task == 'classify':
        model = YOLO('yolov8n-cls.pt')  # 使用分类模型
    elif task == 'pose':
        model = YOLO('yolov8n-pose.pt')  # 使用姿态估计模型
    else:
        raise ValueError(f"无效的任务类型: {task}")

    # ====== 根据运行模式执行对应操作 ======
    if mode == 'train':
        # === 训练模型 ===
        model.train(

        # Train settings -------------------------------------------------------------------------------------------------------
            # model=None,  # 指定用于训练的模型文件。接受.pt预训练模型或.yaml配置文件的路径。用于定义模型结构或初始化权重。
            data=r'E:\datasets\斯塔克\NS处理ignore后的yolo数据\SODA-D.yaml',
            # data=None,  # 数据集配置文件的路径（如coco8.yaml）。包含数据集的特定参数，包括训练和验证数据的路径、类名和类数量。
            epochs=300,
            # epochs=100,  # 训练轮数。每个轮次表示对整个数据集的一次完整迭代。调整此值会影响训练时长和模型性能。
            # time=None,  # 最大训练时间（小时）。如果设置，将覆盖epochs参数，允许在指定时长后自动停止训练。适用于时间受限的训练场景。
            patience=20,  # 提前停止训练前的等待轮次数。当验证指标没有改进时，用于防止过拟合。
            #patience=100,  # (int) epochs to wait for no observable improvement for early stopping of training
            batch=8,
            # batch=16,  # 批次大小，控制每次模型权重更新时处理的样本数量。有三种模式：整数设置（如batch=16），自动模式（batch=-1）使用60% GPU内存，或指定使用率比例（batch=0.70）。batch大，训练得快；batch小，泛化能力强。大批量训练容易导致模型过拟合，因为它更准确地捕捉到了训练数据中的噪声。
            imgsz=1280,  #
            # imgsz=640,  # (int | list) 输入图像的大小，train 和 val 模式下为整数（正方形大小），predict 和 export 模式下为列表形式 [高, 宽]。
            # save=True,  # 是否保存训练检查点和最终模型权重。用于恢复训练或模型部署。
            # save_period=-1,  # 模型检查点保存频率，以轮次为单位。-1表示禁用此功能。用于长时间训练时保存中间模型。 save_period=10表示每经过指定轮次（如10轮epochs）保存一次模型检查点。这对长时间训练有用，可以保存模型的中间状态，以防训练中断或需要回溯到较早的状态。
            # cache=False,  # 启用数据集图像缓存：内存（True/ram）、磁盘（disk）或禁用（False）。提高训练速度，但增加内存占用。
            device=0,
            # device=None,  # 指定用于训练的设备：单个GPU（device=0）、多个GPU（device=0,1）、CPU（device=cpu）或Apple硅设备（device=mps）。


            #workers=8,  # 数据加载的工作线程数（对于多GPU训练每个RANK）。影响数据预处理和输入模型的速度，尤其在多GPU设置下有用。
            project=r'E:\datasets\斯塔克\NS训练结果',
            # project=None,  # 保存训练输出的项目目录名。用于不同实验的有组织存储。
            name='ns训练ignore2',
            # name=None,  # 训练运行的名称。用于在项目文件夹中创建子目录，保存训练日志和输出。
            # exist_ok=False,  # 如果为True，允许覆盖现有的项目/名称目录。用于无需手动清理先前输出的迭代实验。

            # pretrained=True,  # (bool | str) 是否从预训练模型开始训练。可以是布尔值或特定模型的路径，用于加载权重。提高训练效率和模型性能。是否使用预训练模型 (bool)，或者从指定的模型权重文件加载权重 (str)"path/to/weights.pt": 从指定路径加载自定义的权重文件
            # optimizer='auto',  # 训练的优化器选择。可选项包括SGD, Adam, AdamW, NAdam, RAdam, RMSProp等，或根据模型配置自动选择（auto）。影响收敛速度和稳定性。
            verbose=True,
            # verbose=False,  # 启用训练期间的详细输出，提供详细的日志和进度更新。适用于调试和密切监控训练过程。
            # seed=0,  # 设置训练的随机种子，确保相同配置下结果的可重复性。

            # deterministic=True,  # 强制使用确定性算法，确保可重复性，但可能影响性能和速度，因为限制了非确定性算法的使用。
            # single_cls=False,  # 在多类数据集中将所有类别视为单一类别进行训练。适用于二元分类任务或专注于对象存在而非分类时。
              #数据集会按图像的长宽比分组加载，尽量减少填充,建议在图像长宽比一致或相近，需要高效利用计算资源时使用; rect=False（默认），数据集会随机加载图像，所有图像被调整为固定尺寸并填充为方形，建议图像长宽比差异较大时使用，
            # rect=False,  # 启用矩形训练，优化批次组合以最小化填充。可以提高效率和速度，但可能影响模型精度。启用矩形训练模式，使训练时的图像和目标框保持原始的长宽比，而不强制将图像调整为固定的方形，False会避免数据加载顺序引入偏差。
            cos_lr=True,
            # cos_lr=False,  # 使用余弦学习率调度器，按照余弦曲线调整学习率。帮助更好地管理学习率，以便更好地收敛。
            # close_mosaic=10,  # 在最后N个轮次中禁用马赛克数据增强，以在训练完成前稳定模型。设置为0禁用此功能。
            #resume=True,
            # resume=False,  # 从最后保存的检查点恢复训练。自动加载模型权重、优化器状态和轮次计数，继续无缝训练。
            # amp=True,  # 启用自动混合精度（AMP）训练，减少内存占用并可能加速训练，对精度影响较小。
            # fraction=1.0,  # 指定用于训练的数据集比例。允许在资源有限时对数据集子集进行训练，适用于实验。将 fraction 设置为 0.5 意味着只使用一半的数据集进行训练。

            # profile=False,  # 启用训练期间的ONNX和TensorRT速度分析，用于优化模型部署。(部署相关的参数)用于确定是否在训练过程中启动性能分析，并将结果记录到日志中。
            # freeze=None,  # 冻结模型的前N层或指定的层索引，减少可训练参数。适用于微调或迁移学习。freeze=10表示冻结模型的前10层，这些层的权重不会在训练过程中更新。freeze=[0, 1, 2, 5]表示冻结第0、1、2和5层。
            #multi_scale=True,
            # multi_scale= False,  # 多尺度训练,模型在训练时会被迫适应不同大小的输入图像


        # Val/Test settings ----------------------------------------------------------------------------------------------------
            #val= True,  # (bool) validate/test during training
            #split= 'val',  # (str) 指定验证时使用的数据集分区, i.e. 'val', 'test' or 'train'
            #save_json= False,  # (bool) 是否将验证结果保存为 JSON 文件
            
            #save_hybrid= False,  # (bool) 是否保存混合版本的标签（标签 + 额外预测信息）
            #conf= 0.001 ,# (float, optional) 目标检测的置信度阈值 (default 0.25 predict, 0.001 val)
            #iou= 0.7,  # (float) intersection over union (IoU) threshold for NMS,非极大值抑制（NMS）的交并比（IoU）阈值,较高的阈值（0.7）保留更多重叠框，适合密集目标。较低的阈值（如 0.3）倾向于移除更多重叠框，适合稀疏目标。0.0：保留所有检测结果。1.0：只保留置信度最高的结果。
            #max_det= 300,  # (int) 每张图片的最大检测目标数量
            #half= False,  # (bool) 是否使用半精度（FP16），通常用于节省显存
            #dnn= False, # (bool) 是否使用 OpenCV 的 DNN 模块进行 ONNX 推理
            #plots= True,  # (bool) 在训练或验证时是否保存可视化图表和图片

        # Visualize settings ---------------------------------------------------------------------------------------------------

            #show=True,
            # show= False,  # (bool) 如果环境允许，是否显示预测后的图像或视频
            # save_frames= False,  # (bool) 是否保存预测视频的每一帧图像

            # save_txt= False,  # (bool) 是否将预测结果保存为 .txt 文件

            # save_conf= False,  # (bool) 是否在保存的结果中包含置信度分数
            # save_crop= False,  # (bool) 是否保存预测目标的裁剪图像
            # show_labels= True,  # (bool) 是否在预测结果中显示目标类别标签，例如 'person'
            # show_conf= True,  # (bool) 是否在预测结果中显示置信度分数，例如 '0.99'
            # show_boxes= True,  # (bool) 是否在预测结果中显示边界框
            # line_width=  # (int, optional) 边界框的线宽。如果设置为 None，线宽会根据图像大小自动调整


        # Augmentation Settings and Hyperparameters ----------------------------------------------------------------------------------------------------
            # lr0=0.01,  # 初始学习率（如SGD=1E-2，Adam=1E-3）。调整此值对优化过程至关重要，影响模型权重更新的速度。
            # lrf=0.01,  # 最终学习率（初始学习率的分数），与调度器配合使用以随时间调整学习率。lrf=0.01 表示最终学习率是初始学习率的 1%。
            # momentum=0.937,  # SGD的动量因子或Adam优化器的beta1参数，影响当前更新中过去梯度的使用。动量在优化过程中用于加速收敛并减少训练中的震荡。它通过结合前几次的梯度方向，帮助模型更稳健地更新参数。
            # weight_decay=0.0005,  # L2正则化项，通常也称为权重衰减，惩罚较大的权重以防止过拟合。模型仍然存在过拟合问题，可以尝试增加weight_decay值；相反，如果模型表现不够好，可能需要减小这个值。
            # warmup_epochs=3.0,  # 学习率预热的轮次，从低值逐渐增加到初始学习率，以稳定早期训练。
            # warmup_momentum=0.8,  # 预热阶段的初始动量，在预热期内逐渐调整到设定的动量。
            # warmup_bias_lr=0.1,  # 预热阶段的偏差参数学习率，有助于稳定初始轮次中的模型训练。
            # box=7.5,  # 损失函数中边界框回归损失的权重，影响准确预测边界框坐标的重要性，边界框精度，关注边界框的整体位置和大小。
            # cls=0.5,  # 损失函数中的分类损失权重，影响相对于其他组件正确预测类别的重要性。
            # dfl=1.5,  # 分布焦点损失（Distribution Focal Loss, DFL）的权重，用于边界框回归，帮助模型更准确地预测连续坐标，边界框预测的细粒度精度。
            ## pose=12.0,  # 用于姿态估计模型中姿态损失的权重，影响准确预测姿态关键点的重要性。
            ## kobj=1.0,  # 姿态估计模型中关键点目标性损失的权重，平衡检测置信度与姿态准确性。
            # label_smoothing=0.0,  # 应用标签平滑，将硬标签软化为目标标签和标签均匀分布的混合，能够提高泛化能力。设置为0.0表示不使用标签平滑；如果需要应用标签平滑，可以将其设置为一个小于1的值。  对于：1 数据集标签干净、无噪声；2 类别分布严重不均衡；3 单类别任务或仅需要硬标签的场景。不用标签平滑
            # nbs=64,  # 用于损失标准化的名义批量大小。
            # hsv_h=0.015,  # 调整图像色调，增加颜色变化性，帮助模型在不同光照条件下泛化，范围：0.0 - 1.0
            # hsv_s=0.7,  # 调整图像饱和度，模拟不同环境条件下的颜色强度，范围：0.0 - 1.0
            # hsv_v=0.4,  # 调整图像亮度，帮助模型在各种光照条件下表现良好，范围：0.0 - 1.0，对于灰度图像，只有hsv_v有效
            #degrees=90,
            # degrees=0.0,  # 随机旋转图像，增强模型识别不同方向物体的能力，范围：-180 - +180
            # translate=0.1,  # 平移图像，帮助模型学习检测部分可见的物体，范围：0.0 - 1.0。0.1 表示最大平移 10% 的图像尺寸。
            #scale=0.2,
            # scale=0.5,  # 缩放图像，模拟不同距离的物体，范围：>=0.0

            #shear=90,
            # shear=0.0,  # 剪切图像，模拟从不同角度观察物体的效果，范围：-180 - +180，图像倾斜

            # perspective=0.0,  # 应用随机透视变换，增强模型理解3D空间中的物体能力，范围：0.0 - 0.001，图像扭曲
            #flipud=0.5,
            # flipud=0.0,  # 垂直翻转图像，增加数据多样性，范围：0.0 - 1.0。0.5表示50%的概率对图像进行垂直翻转

            # fliplr=0.5,  # 水平翻转图像，有助于学习对称物体和增加数据集多样性，范围：0.0 - 1.0
            # bgr=0.0,  # 将图像通道从RGB翻转到BGR，增加对错误通道顺序的鲁棒性，范围：0.0 - 1.0
            mosaic=0.2,
            # mosaic=1.0,  # 将四张训练图像合成一张，模拟不同场景组合和物体交互，范围：0.0 - 1.0

            # mixup=0.0,  # 将两张图像及其标签混合，增强模型泛化能力，范围：0.0 - 1.0
            
            # copy_paste=0.0,  # 从一张图像中复制对象并粘贴到另一张图像，增加物体实例数并学习遮挡，范围：0.0 - 1.0

            ## auto_augment='randaugment',  # 自动应用预定义增强策略，优化分类任务，选项：['randaugment', 'autoaugment', 'augmix']
            # erasing=0.4,  # 随机擦除图像的一部分，鼓励模型专注于更不明显的特征，范围：0.0 - 0.9
           
            # crop_fraction=1.0  # 裁剪图像，强调中心特征，减少背景干扰，范围：0.1 - 1.0，表示裁剪后的图像占原图像的比例。
        )


    elif mode == 'val':
        # === 验证模型 ===
        results = model.val(
            data='D:/YOLO/hit-avu/hit-uav.yaml',  # 数据集配置文件路径
            batch=4,
            # batch=16,  # 批次大小，控制一次传入模型的图像数量
            # imgsz=640,  # 输入图像尺寸，所有图像会被调整到该大小
            # device='0',  # 使用的设备，例如 '0' 表示 GPU 0，'cpu' 表示使用 CPU
            # split='val',  # 验证子集，支持 'train'、'val' 或 'test'
            # conf=0.001,  # 检测的置信度阈值（默认 0.001），小于此值的目标会被忽略
            # iou=0.7,  # 非极大值抑制（NMS）的 IoU 阈值，控制是否合并边界框
            # max_det=300,  # 每张图像的最大检测目标数量
            # plots=True,  # 是否生成可视化图表，包括 mAP 曲线和示例预测
            save_json=True,
            # save_json=False,  # 是否将验证结果保存为 COCO 格式的 JSON 文件
            save_hybrid=True,
            # save_hybrid=False,  # 是否保存混合版本的标签（真实标签 + 预测结果）
            # half=False,  # 是否使用半精度（FP16）验证，减少显存占用
            # dnn=False  # 是否使用 OpenCV 的 DNN 推理引擎（仅适用于 ONNX 模型）
            # classes=None,  # (int | list[int], optional) 按类别筛选验证结果，默认为 None，表示验证所有类别
            # embed=None,  # (list[int], optional) 返回指定层的特征向量或嵌入，默认为 None
        )

        print("验证结果:", results)


    elif mode == 'predict':

        # === 预测新数据 ===

        import os
        import cv2

        input_path = r"F:\STARK\SODA\SODA-D\划分为yolo格式\images\test"  # 输入数据路径，可以是图片、视频、文件夹或摄像头（0 表示默认摄像头）

        # 自动识别输入类型

        is_camera = str(input_path).isdigit()  # 如果是数字，则判断为摄像头输入
        file_ext = None if is_camera else os.path.splitext(input_path)[-1].lower()  # 获取文件扩展名

        try:

            results = model.predict(
                source=input_path,
                imgsz=[3840,2500],
                #imgsz=[2160,3840],
                # imgsz=640,  # (int | list[int]) 输入图像大小，整数表示正方形图像，列表表示 [高, 宽]
                # conf=0.25,  # (float) 置信度阈值，低于此值的目标会被过滤
                iou=0.5,
                # iou=0.7,  # (float) IOU 阈值，用于非极大值抑制（NMS）
                # max_det=300,  # (int) 每张图像的最大检测目标数量
                 device='0',  # (str) 使用的设备：GPU编号（如 '0'），'cpu' 表示使用 CPU

                 save=True,
                #save=False,  # (bool) 是否保存预测结果
                project=r'F:\STARK\SODA\SODA-D\yolo未训练识别结果',  #保存输出的项目目录名

                # name='1280',
                # save_txt=False,  # (bool) 是否将预测边界框保存到文本文件
                # save_conf=False,  # (bool) 是否将预测目标的置信度分数保存到文本文件
                #save_crop=True,
                # save_crop=False,  # (bool) 是否保存预测到的目标裁剪图像
                # save_frames=False,  # (bool) 是否保存预测后的视频逐帧图像

                # show=False,  # (bool) 是否显示预测后的图像和视频

                #stream=True,    #检测图片时，最好关闭，否则会卡住，视屏流再启用
                # stream_buffer=False,  # (bool) 是否缓存所有视频流帧，True 表示缓存，False 表示仅返回最新帧
                # vid_stride=1,  # (int) 视频帧率步长，指定处理视频时的帧跳跃间隔

                # visualize=False,  # (bool) 是否保存模型特征图可视化
                 augment=True,
                # augment=False,  # (bool) 是否对输入数据应用数据增强，例如翻转或裁剪
                # agnostic_nms=False,  # (bool) 是否使用类别无关的非极大值抑制

                #classes=[0, 1, 2],
                # classes=None,  # (int | list[int], optional) 按类别筛选检测结果，None 表示不筛选, classes=[0, 2, 3]表示只检测类别 0、2 和 3 的目标。
                ## retina_masks=False,  # (bool) 是否生成高分辨率分割掩码，仅适用于分割任务

                # embed=None,  # (list[int], optional) 返回指定层的特征向量或嵌入，默认为 None

                # plots=True,  # (bool) 是否生成预测结果的可视化图表
                # half=False,  # (bool) 是否使用半精度（FP16）推理，减少显存占用
                # dnn=False,  # (bool) 是否使用 OpenCV 的 DNN 推理引擎，仅适用于导出的 ONNX 模型

                #show_labels=False,
                # show_labels=True,  # (bool) 是否显示预测目标的类别标签（如 'person'）

                #show_conf=False,
                # show_conf=True,  # (bool) 是否显示预测目标的置信度分数（如 '0.99'）
                # show_boxes=True,  # (bool) 是否显示预测目标的边界框

                 #line_width=1,
                # line_width=None,  # (int, optional) 边界框的线宽，默认为 None 表示根据图像大小自动调整

                # batch=1  # (int) 批次大小，指定每次推理处理的图像或视频帧数量
            )

            # 统一处理检测结果
            window_name = "Detection Result"

            for result in results:
                img = result.plot()  # 绘制检测结果
                cv2.imshow(window_name, img)  # 显示检测结果

                if is_camera or file_ext in ['.mp4', '.avi', '.mov']:  # 视频或摄像头输入
                    if cv2.waitKey(1) & 0xFF == ord('q'):  # 每帧等待 1ms，按 'q' 键退出
                        break
                elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tif']:  # 图片输入
                    print("按任意键退出图片显示...")
                    cv2.waitKey(0)  # 图片模式下等待按键关闭窗口
                    break  # 显示一张图片后退出
                elif os.path.isdir(input_path):  # 如果输入是文件夹
                    if cv2.waitKey(1) & 0xFF == ord('q'):  # 每帧等待 1ms，按 'q' 键退出
                        break

            cv2.destroyAllWindows()  # 关闭所有窗口

        except Exception as e:
            print(f"错误：{e}")


        finally:

            if 'results' in locals() and hasattr(results, "close"):
                results.close()

        print("预测完成", results)


    elif mode == 'export':
        # === 导出模型 ===
        model.export(
            # format='onnx',  # (str) 导出格式，可选：onnx、torchscript、engine、coreml 等
            imgsz=[640, 640],
            # imgsz=640,  # (int | list[int]) 输入图像尺寸，整数表示正方形尺寸，列表表示 [高, 宽]
            # dynamic=False,  # (bool) 是否启用动态批次和大小，适用于支持动态输入的导出格式
            # simplify=False,  # (bool) 是否简化导出后的模型，减少计算复杂度
            # int8=False,  # (bool) 是否量化为 INT8 精度（需要支持的硬件和量化工具链）
            # half=False,  # (bool) 是否量化为 FP16 精度，减少显存占用
            # optimize=False,  # (bool) 是否对模型进行优化（仅支持 ONNX 和 TensorRT）
            # workspace=4,  # (int) TensorRT 工作空间大小（仅适用于 TensorRT 导出）
            # opset=12,  # (int) ONNX 的 opset 版本（默认 12）
            # dynamic_batch=False,  # (bool) 是否启用动态批次（适用于 TensorRT）
            # export_nms=False,  # (bool) 是否在导出时保留非极大值抑制（NMS），默认不保留
            # grid=False,  # (bool) 是否保留模型中的网格信息，适用于某些导出格式
            # topk_per_class=100,  # (int) 每类别的最大检测目标数量（仅适用于 TensorRT）
            # topk_all=100,  # (int) 全类别的最大检测目标数量（仅适用于 TensorRT）
            # include_nms=False,  # (bool) 是否在导出模型中包含 NMS 后处理（部分格式支持）
            # dnn=False  # (bool) 是否使用 OpenCV 的 DNN 推理引擎
        )
        print("模型已导出")

    elif mode == 'track':
        # === 跟踪目标 ===
        results = model.track(
            source='path/to/video',  # (str) 输入数据路径，可以是视频文件、文件夹或摄像头设备
            # imgsz=640,  # (int | list[int]) 输入图像大小，整数表示正方形尺寸，列表表示 [高, 宽]
            # conf=0.25,  # (float) 置信度阈值，低于此值的目标会被过滤
            # iou=0.45,  # (float) IOU 阈值，用于非极大值抑制（NMS）
            # max_det=1000,  # (int) 每帧图像的最大检测目标数量
            # device='0',  # (str) 使用的设备，例如 '0' 表示 GPU 0，'cpu' 表示使用 CPU
            # save=True,  # (bool) 是否保存追踪结果的视频文件
            # save_txt=False,  # (bool) 是否保存追踪结果为文本文件
            # save_conf=False,  # (bool) 是否保存置信度分数到文本文件
            # save_crop=False,  # (bool) 是否保存裁剪后的目标
            # show=True,  # (bool) 是否显示实时追踪结果
            # stream_buffer=False,  # (bool) 是否缓存所有视频流帧，True 表示缓存，False 表示仅返回最新帧
            # vid_stride=1,  # (int) 视频帧率步长，指定处理视频时的帧跳跃间隔

            # tracker='botsort.yaml',  # (str) 追踪器配置文件路径，定义追踪算法和参数
            # reid_model=None,  # (str) ReID 模型路径，用于目标外观特征提取（如 'osnet' 模型）
            # visualize=False,  # (bool) 是否保存追踪特征图可视化
            # augment=False,  # (bool) 是否对输入数据应用数据增强
            # half=False,  # (bool) 是否使用半精度（FP16）推理
            # dnn=False,  # (bool) 是否使用 OpenCV 的 DNN 推理引擎
        )

        print("跟踪完成:", results)

    elif mode == 'benchmark':
        # === 性能测试 ===
        model.benchmark(
            imgsz=640,  # (int | list[int]) 输入图像尺寸，整数表示正方形尺寸，列表表示 [高, 宽]
            half=False,  # (bool) 是否使用半精度（FP16），减少显存占用，提高推理速度
            device='0',  # (str) 使用的设备：GPU编号（如 '0'），'cpu' 表示使用 CPU
            batch=1,  # (int) 批次大小，影响每次推理的图像数量
            repetitions=100  # (int) 运行的推理次数，用于统计平均性能
        )
        print("性能测试完成")

    else:
        print(f"无效的模式: {mode}. 请选择 'train', 'val', 'predict', 'export', 'track', 或 'benchmark'.")
