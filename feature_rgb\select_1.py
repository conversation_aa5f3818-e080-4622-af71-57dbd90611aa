# from sklearn.linear_model import Lasso
# from sklearn.feature_selection import SelectFromModel
# from sklearn.linear_model import LogisticRegression
# from sklearn.datasets import make_classification
# from sklearn.pipeline import Pipeline
# from sklearn.model_selection import GridSearchCV
# # 生成示例数据
# import numpy as np

# 注意：请勿将此脚本命名为 select.py，以避免与 Python 标准库 select 模块冲突。

# # 假设您有以下数据（实际需替换为您的特征提取结果）
# num_images = 100  # 共100张图像
# sift_features = [np.random.rand(128) for _ in range(num_images)]    # 每张图像的SIFT特征（128维）
# asift_features = [np.random.rand(128) for _ in range(num_images)]   # ASIFT特征（128维）
# texture_features = [np.random.rand(50) for _ in range(num_images)]  # 纹理特征（50维）
# edge_features = [np.random.rand(30) for _ in range(num_images)]     # 边缘特征（30维）

# # 将每张图像的所有特征拼接成一个向量
# X = []
# for i in range(num_images):
#     img_features = np.concatenate([
#         sift_features[i],
#         asift_features[i],
#         texture_features[i],
#         edge_features[i]
#     ])
#     X.append(img_features)

# X = np.array(X)  # 转换为矩阵，形状 (100, 336)

# # 假设前50张是类别0（猫），后50张是类别1（狗）
# y = np.array([0] * 50 + [1] * 50)

# # 或者从文件中加载标签（实际场景常用）
# # y = np.loadtxt("labels.txt")

# from sklearn.preprocessing import StandardScaler
# scaler = StandardScaler()
# X_scaled = scaler.fit_transform(X)

# # 使用L1逻辑回归选择特征
# logreg = LogisticRegression(penalty='l1', solver='liblinear', C=0.1, max_iter=1000)
# selector = SelectFromModel(logreg).fit(X_scaled, y)
# X_selected = selector.transform(X_scaled)

# # 输出结果
# print("原始特征维度:", X.shape[1])
# print("筛选后特征维度:", X_selected.shape[1])
# print("被选中的特征索引:", selector.get_support(indices=True))

# # 查看哪些特征类型被保留（假设知道特征分块）
# selected_indices = selector.get_support(indices=True)
# sift_selected = np.sum((selected_indices >= 0) & (selected_indices < 128))
# asift_selected = np.sum((selected_indices >= 128) & (selected_indices < 256))
# texture_selected = np.sum((selected_indices >= 256) & (selected_indices < 306))
# edge_selected = np.sum((selected_indices >= 306))
# print(f"SIFT特征保留: {sift_selected}/128, ASIFT保留: {asift_selected}/128, 纹理保留: {texture_selected}/50, 边缘保留: {edge_selected}/30")


# pipeline = Pipeline([
#     ('scaler', StandardScaler()),
#     ('selector', SelectFromModel(LogisticRegression(penalty='l1', solver='liblinear'))),
#     ('classifier', LogisticRegression())
# ])
# param_grid = {'selector__estimator__C': [0.1, 1.0, 10.0]}
# search = GridSearchCV(pipeline, param_grid, cv=5)
# search.fit(X, y)




# # 使用逻辑回归进行特征选择


# logreg = LogisticRegression(penalty='l1', solver='liblinear', C=0.1)
# selector = SelectFromModel(logreg).fit(X, y)
# X_selected = selector.transform(X)

# print("原始特征维度:", X.shape[1])
# print("选择后特征维度:", X_selected.shape[1])
# print("被选中的特征索引:", selector.get_support(indices=True))



# feature_images/
# ├── sift/          # SIFT特征可视化图像
# │   ├── image1.jpg
# │   ├── image2.jpg
# │   └── ...
# ├── texture/       # 纹理特征图像
# │   ├── image1.jpg
# │   ├── image2.jpg
# │   └── ...
# └── labels.txt     # 标签文件

# image_names = ["image1.jpg", "image2.jpg", "image3.jpg"]
# labels = [0, 1, 0]

# with open("labels.txt", "w") as f:
#     for name, label in zip(image_names, labels):
#         f.write(f"{name} {label}\n")


import os
import cv2
import numpy as np
from skimage.feature import local_binary_pattern, hog
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectFromModel
from sklearn.linear_model import LogisticRegression

# 配置路径
base_dir = "D:\\vscode_mission\\feature_rgb\\data\\feature_images"
feature_types = ["harris", "brisk", "sift"]  # 根据实际文件夹调整

# 工具函数：从图像提取特征
def extract_features(img):
    """从单张特征图像提取数值特征"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 示例1：直接使用像素值（简单但不推荐）
    # features = gray.flatten()  # 展平为向量
    
    # 示例2：提取纹理特征（LBP）
    lbp = local_binary_pattern(gray, P=8, R=1, method="uniform")
    hist, _ = np.histogram(lbp, bins=np.arange(0, 10), density=True)
    
    # 示例3：提取HOG特征
    hog_feat = hog(gray, orientations=8, pixels_per_cell=(16, 16), cells_per_block=(1, 1))
    
    return np.concatenate([hist, hog_feat])

# 加载所有图像并提取特征
X = []
image_ids = []
for i in range(len(feature_types)):
    for img_name in os.listdir(os.path.join(base_dir, feature_types[i])):
        print(f"处理图像: {img_name}")
        img_id = img_name.split(".")[0]
        image_ids.append(img_id)
        
        # 加载所有特征图像并拼接
        all_features = []
        for feat_type in feature_types:
            img_path = os.path.join(base_dir, feat_type, img_name)
           
            img = cv2.imread(img_path)
            if img is None:
                raise ValueError(f"无法加载图像: {img_path}")
            features = extract_features(img)
            all_features.append(features)
        
        X.append(np.concatenate(all_features))

X = np.array(X)

# 从labels.txt加载标签（格式：image1 0）
label_dict = {}
with open(os.path.join(base_dir, "labels.txt")) as f:
    for line in f:
        img_id, label = line.strip().split()
        label_dict[img_id] = int(label)
        print("label_dict[img_id]",label_dict[img_id])

print("label_dict",label_dict)
print(image_ids)
print(label_dict.keys())

y = np.array([label_dict[img_id + '.jpg'] for img_id in image_ids])


# 标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 特征选择
logreg = LogisticRegression(penalty="l1", solver="liblinear", C=5.0)
selector = SelectFromModel(logreg).fit(X_scaled, y)
X_selected = selector.transform(X_scaled)

print(f"原始特征维度: {X.shape[1]}, 筛选后: {X_selected.shape[1]}")


import matplotlib.pyplot as plt
from sklearn.decomposition import PCA

logreg.fit(X_scaled, y)  # 确保已训练
coef = logreg.coef_.flatten()

# 绘制特征重要性图
plt.figure(figsize=(12, 6))
plt.bar(range(len(coef)), np.abs(coef))
plt.title("Feature Importance (L1 Logistic Regression Coefficients)")
plt.xlabel("Feature Index")
plt.ylabel("Absolute Coefficient Value")
plt.show()
# # 使用更高级的特征提取方法
# def extract_deep_features(img):
#     # 使用预训练CNN（如ResNet）提取特征
#     from tensorflow.keras.applications import ResNet50
#     model = ResNet50(weights="imagenet", include_top=False, pooling="avg")
#     img_processed = preprocess_input(img)  # 需根据模型调整预处理
#     return model.predict(img_processed[np.newaxis, ...]).flatten()