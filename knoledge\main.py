import numpy as np
import cv2
import torch
import torch.nn as nn
import torch.optim as optim
from torch_geometric.nn import GCNConv
import networkx as nx
import clip
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
import matplotlib.pyplot as plt
from datetime import datetime
from data_processor import DataProcessor
from feature_extractor import FeatureExtractor
from detection_scheduler import DetectionScheduler
from knowledge_graph import KnowledgeGraph
from target_recognizer import TargetRecognizer
class KnowledgeDataDrivenTargetRecognition:
    def __init__(self, config=None):
        # 配置参数
        self.config = config or {
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'feature_dim': 512,
            'scene_types': ['sunny', 'rainy', 'night', 'foggy'],
            'target_classes': ['carrier', 'destroyer', 'frigate', 'submarine', 'speedboat']
        }
        
        # 初始化各模块
        self.data_processor = DataProcessor(self.config)
        self.feature_extractor = FeatureExtractor(self.config)
        self.detection_scheduler = DetectionScheduler(self.config)
        self.knowledge_graph = KnowledgeGraph(self.config)
        self.target_recognizer = TargetRecognizer(self.config)
        
        # 性能记录
        self.performance_history = []
    
    def process_scene(self, multi_source_data, environmental_data):
        """处理一个场景的完整流程"""
        # 1. 数据预处理
        preprocessed_data = self.data_processor.preprocess(multi_source_data)
        
        # 2. 特征提取
        features = self.feature_extractor.extract_features(preprocessed_data)
        
        # 3. 特征评估
        feature_metrics = self.feature_extractor.evaluate_features(features)
        
        # 4. 场景分析
        scene_type, feature_sensitivity = self.data_processor.analyze_scene(
            preprocessed_data, environmental_data
        )
        
        # 5. 探测体制调度
        detection_weights = self.detection_scheduler.calculate_detection_weights(
            feature_metrics, scene_type, feature_sensitivity
        )
        
        # 6. 特征调度
        fused_features = self.detection_scheduler.schedule_features(
            features, detection_weights
        )
        
        # 7. 知识约束应用
        knowledge_constraints = self.knowledge_graph.get_constraints(scene_type)
        
        # 8. 目标识别决策
        recognition_result = self.target_recognizer.recognize_target(
            fused_features, knowledge_constraints
        )
        
        # 9. 记录性能
        self._record_performance(recognition_result, scene_type)
        
        return recognition_result
    
    def _record_performance(self, result, scene_type):
        """记录识别性能"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.performance_history.append({
            'timestamp': timestamp,
            'scene_type': scene_type,
            'confidence': result['confidence'],
            'predicted_class': result['predicted_class']
        })
    
    def visualize_performance(self):
        """可视化性能指标"""
        # 示例：绘制识别置信度随时间的变化
        if not self.performance_history:
            print("No performance history to visualize")
            return
        
        timestamps = [entry['timestamp'] for entry in self.performance_history]
        confidences = [entry['confidence'] for entry in self.performance_history]
        
        plt.figure(figsize=(10, 6))
        plt.plot(timestamps, confidences, 'o-')
        plt.title('Recognition Confidence Over Time')
        plt.xlabel('Timestamp')
        plt.ylabel('Confidence')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

# 主函数
if __name__ == "__main__":
    # 初始化系统
    system = KnowledgeDataDrivenTargetRecognition()
    
    # 模拟输入数据
    multi_source_data = {
        'visible': np.random.rand(224, 224, 3),
        'infrared': np.random.rand(224, 224, 1),
        'sar': np.random.rand(224, 224, 1)
    }
    
    environmental_data = {
        'weather': 'sunny',
        'time': 'day',
        'sea_state': 'calm',
        'interference': 'none'
    }
    
    # 处理场景
    result = system.process_scene(multi_source_data, environmental_data)
    
    # 输出结果
    print(f"识别结果: {result['predicted_class']}")
    print(f"置信度: {result['confidence']:.2f}")
    print(f"决策依据: {result['decision_basis']}")
    
    # 可视化性能
    system.visualize_performance()    