import sys
import os
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib
matplotlib.use('Qt5Agg')
plt.rcParams['figure.figsize'] = [10, 6]
plt.rcParams['figure.dpi'] = 100

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QFileDialog, QTabWidget, QSplitter, 
                            QComboBox, QGroupBox, QGridLayout, QCheckBox, QSlider,
                            QMessageBox, QSpinBox, QDoubleSpinBox, QTabWidget)
from PyQt5.QtGui import QPixmap, QImage, QFont
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QPoint
from datetime import datetime

class FeatureExtractor:
    def __init__(self):
        self.feature_names = ['结构相似度', '信息熵', '平均灰度', '对比度', '高频复杂度', '能量']
        
    def calculate_features(self, img, ref_img=None):
        """计算图像的各项特征"""
        if len(img.shape) > 2:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        features = {}
        
        # 结构相似度 (需要参考图像)
        if ref_img is not None:
            if len(ref_img.shape) > 2:
                ref_img = cv2.cvtColor(ref_img, cv2.COLOR_BGR2GRAY)
            features['结构相似度'] = self._calculate_ssim(img, ref_img)
        else:
            features['结构相似度'] = 0.0
            
        # 信息熵
        features['信息熵'] = self._calculate_entropy(img)
        
        # 平均灰度
        features['平均灰度'] = np.mean(img) / 255.0
        
        # 对比度
        features['对比度'] = self._calculate_contrast(img)
        
        # 高频复杂度
        features['高频复杂度'] = self._calculate_high_frequency(img)
        
        # 能量
        features['能量'] = self._calculate_energy(img)
        
        return features
    
    def _calculate_ssim(self, img1, img2):
        """计算结构相似度"""
        C1 = (0.01 * 255) ** 2
        C2 = (0.03 * 255) ** 2
        
        img1 = img1.astype(np.float64)
        img2 = img2.astype(np.float64)
        kernel = cv2.getGaussianKernel(11, 1.5)
        window = np.outer(kernel, kernel.transpose())
        
        mu1 = cv2.filter2D(img1, -1, window)[5:-5, 5:-5]  # valid
        mu2 = cv2.filter2D(img2, -1, window)[5:-5, 5:-5]
        mu1_sq = mu1 ** 2
        mu2_sq = mu2 ** 2
        mu1_mu2 = mu1 * mu2
        sigma1_sq = cv2.filter2D(img1 ** 2, -1, window)[5:-5, 5:-5] - mu1_sq
        sigma2_sq = cv2.filter2D(img2 ** 2, -1, window)[5:-5, 5:-5] - mu2_sq
        sigma12 = cv2.filter2D(img1 * img2, -1, window)[5:-5, 5:-5] - mu1_mu2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        return np.mean(ssim_map)
    
    def _calculate_entropy(self, img):
        """计算信息熵"""
        hist = cv2.calcHist([img], [0], None, [256], [0, 256])
        hist_norm = hist.ravel() / hist.sum()
        non_zero = hist_norm[hist_norm > 0]
        entropy = -np.sum(non_zero * np.log2(non_zero))
        return entropy / 8.0  # 归一化到 [0, 1]
    
    def _calculate_contrast(self, img):
        """计算对比度"""
        mean = np.mean(img)
        std = np.std(img)
        return std / 255.0  # 归一化到 [0, 1]
    
    def _calculate_high_frequency(self, img):
        """计算高频复杂度"""
        f = np.fft.fft2(img)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift))
        
        # 计算低频和高频区域的能量
        rows, cols = img.shape
        crow, ccol = rows // 2, cols // 2
        
        # 低频区域 (中心部分)
        fshift_low = fshift.copy()
        fshift_low[crow-30:crow+30, ccol-30:ccol+30] = 0
        low_energy = np.sum(np.abs(fshift_low) ** 2)
        
        # 高频区域 (边缘部分)
        fshift_high = fshift.copy()
        fshift_high[0:rows, 0:cols] = 0
        fshift_high[crow-30:crow+30, ccol-30:ccol+30] = fshift[crow-30:crow+30, ccol-30:ccol+30]
        high_energy = np.sum(np.abs(fshift_high) ** 2)
        
        # 高频能量占比作为高频复杂度指标
        total_energy = low_energy + high_energy
        if total_energy == 0:
            return 0.0
        return high_energy / total_energy
    
    def _calculate_energy(self, img):
        """计算能量"""
        img_normalized = img / 255.0
        energy = np.sum(img_normalized ** 2) / (img.shape[0] * img.shape[1])
        return energy
    
    def extract_edge_features(self, img):
        """提取边缘特征"""
        if len(img.shape) > 2:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(img, 100, 200)
        return edges
    
    def extract_corner_features(self, img):
        """提取角点特征"""
        if len(img.shape) > 2:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        gray = np.float32(gray)
        dst = cv2.cornerHarris(gray, 2, 3, 0.04)
        dst = cv2.dilate(dst, None)
        corner_img = img.copy()
        corner_img[dst > 0.01 * dst.max()] = [0, 0, 255]
        return corner_img
    
    def extract_frequency_features(self, img):
        """提取频率特征"""
        if len(img.shape) > 2:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 傅里叶变换
        f = np.fft.fft2(img)
        fshift = np.fft.fftshift(f)
        magnitude_spectrum = 20 * np.log(np.abs(fshift))
        
        # 归一化显示
        magnitude_spectrum = cv2.normalize(magnitude_spectrum, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        return magnitude_spectrum
    
    def extract_intensity_curve(self, img):
        """提取灰度曲线"""
        if len(img.shape) > 2:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        hist = cv2.calcHist([img], [0], None, [256], [0, 256])
        return hist

class FeatureVisualizer(QWidget):
    """特征可视化窗口"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout(self)
        
        # 创建Matplotlib图形
        self.fig = Figure(figsize=(10, 8), dpi=100)
        self.canvas = FigureCanvas(self.fig)
        layout.addWidget(self.canvas)
        
        self.clear()
        
    def clear(self):
        """清除图形"""
        self.fig.clear()
        self.canvas.draw()
        
    def update_feature_bar_chart(self, visible_features, ir_features, feature_names):
        """更新特征柱状图"""
        self.fig.clear()
        
        # 创建两个子图
        ax1 = self.fig.add_subplot(121)
        ax2 = self.fig.add_subplot(122)
        
        # 设置柱状图宽度和位置
        bar_width = 0.35
        index = np.arange(len(feature_names))
        
        # 可见光特征柱状图
        ax1.bar(index, visible_features, bar_width, 
                label='可见光', color='royalblue')
        ax1.set_xlabel('特征')
        ax1.set_ylabel('归一化值')
        ax1.set_title('可见光特征')
        ax1.set_xticks(index)
        ax1.set_xticklabels(feature_names, rotation=45, ha='right')
        ax1.legend()
        
        # 红外特征柱状图
        ax2.bar(index, ir_features, bar_width, 
                label='红外', color='firebrick')
        ax2.set_xlabel('特征')
        ax2.set_ylabel('归一化值')
        ax2.set_title('红外特征')
        ax2.set_xticks(index)
        ax2.set_xticklabels(feature_names, rotation=45, ha='right')
        ax2.legend()
        
        self.fig.tight_layout()
        self.canvas.draw()
        
    def update_weight_pie_chart(self, visible_weight, ir_weight):
        """更新权重饼图"""
        self.fig.clear()
        
        ax = self.fig.add_subplot(111)
        
        # 饼图数据
        labels = ['可见光权重', '红外权重']
        sizes = [visible_weight, ir_weight]
        colors = ['royalblue', 'firebrick']
        explode = (0.1, 0)  # 突出显示可见光权重
        
        # 绘制饼图
        ax.pie(sizes, explode=explode, labels=labels, colors=colors,
               autopct='%1.1f%%', shadow=True, startangle=90)
        ax.axis('equal')  # 确保饼图是圆的
        ax.set_title('探测体制权重分配')
        
        self.fig.tight_layout()
        self.canvas.draw()

class FeatureResultWindow(QWidget):
    """特征提取结果子窗口"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("特征提取结果")
        self.resize(800, 600)
        
        self.initUI()
        
    def initUI(self):
        main_layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 原始图像标签页
        self.tab_original = QWidget()
        self.layout_original = QVBoxLayout(self.tab_original)
        self.label_original = QLabel("原始图像")
        self.label_original.setAlignment(Qt.AlignCenter)
        self.layout_original.addWidget(self.label_original)
        self.tab_widget.addTab(self.tab_original, "原始图像")
        
        # 边缘检测标签页
        self.tab_edge = QWidget()
        self.layout_edge = QVBoxLayout(self.tab_edge)
        self.label_edge = QLabel("边缘特征")
        self.label_edge.setAlignment(Qt.AlignCenter)
        self.layout_edge.addWidget(self.label_edge)
        self.tab_widget.addTab(self.tab_edge, "边缘特征")
        
        # 角点检测标签页
        self.tab_corner = QWidget()
        self.layout_corner = QVBoxLayout(self.tab_corner)
        self.label_corner = QLabel("角点特征")
        self.label_corner.setAlignment(Qt.AlignCenter)
        self.layout_corner.addWidget(self.label_corner)
        self.tab_widget.addTab(self.tab_corner, "角点特征")
        
        # 频率特征标签页
        self.tab_frequency = QWidget()
        self.layout_frequency = QVBoxLayout(self.tab_frequency)
        self.label_frequency = QLabel("频率特征")
        self.label_frequency.setAlignment(Qt.AlignCenter)
        self.layout_frequency.addWidget(self.label_frequency)
        self.tab_widget.addTab(self.tab_frequency, "频率特征")
        
        # 灰度曲线标签页
        self.tab_intensity = QWidget()
        self.layout_intensity = QVBoxLayout(self.tab_intensity)
        
        # 创建Matplotlib图形
        self.fig_intensity = Figure(figsize=(6, 4), dpi=100)
        self.canvas_intensity = FigureCanvas(self.fig_intensity)
        self.layout_intensity.addWidget(self.canvas_intensity)
        self.tab_widget.addTab(self.tab_intensity, "灰度曲线")
        
        main_layout.addWidget(self.tab_widget)
        
    def update_features(self, original_img, edge_img, corner_img, frequency_img, intensity_hist):
        """更新特征显示"""
        # 更新原始图像
        self.update_image_label(self.label_original, original_img)
        
        # 更新边缘图像
        self.update_image_label(self.label_edge, edge_img)
        
        # 更新角点图像
        self.update_image_label(self.label_corner, corner_img)
        
        # 更新频率图像
        self.update_image_label(self.label_frequency, frequency_img)
        
        # 更新灰度曲线
        self.update_intensity_curve(intensity_hist)
        
    def update_image_label(self, label, img):
        """更新图像标签"""
        if len(img.shape) == 3:
            height, width, channel = img.shape
            bytesPerLine = 3 * width
            qImg = QImage(img.data, width, height, bytesPerLine, QImage.Format_RGB888).rgbSwapped()
        else:
            height, width = img.shape
            qImg = QImage(img.data, width, height, width, QImage.Format_Grayscale8)
        
        pixmap = QPixmap.fromImage(qImg)
        label.setPixmap(pixmap.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
    def update_intensity_curve(self, hist):
        """更新灰度曲线"""
        self.fig_intensity.clear()
        ax = self.fig_intensity.add_subplot(111)
        ax.plot(hist)
        ax.set_title('灰度直方图')
        ax.set_xlabel('灰度值')
        ax.set_ylabel('像素数量')
        self.fig_intensity.tight_layout()
        self.canvas_intensity.draw()

class VideoProcessor(QThread):
    """视频处理线程"""
    update_signal = pyqtSignal(dict)
    
    def __init__(self, visible_path, ir_path, feature_extractor, parent=None):
        super().__init__(parent)
        self.visible_path = visible_path
        self.ir_path = ir_path
        self.feature_extractor = feature_extractor
        self.is_running = True
        self.frame_count = 0
        self.result_window = None
        
    def run(self):
        """线程运行函数"""
        # 打开视频文件或摄像头
        if self.visible_path.lower().startswith('rtsp') or self.visible_path.isdigit():
            visible_cap = cv2.VideoCapture(int(self.visible_path) if self.visible_path.isdigit() else self.visible_path)
            ir_cap = cv2.VideoCapture(int(self.ir_path) if self.ir_path.isdigit() else self.ir_path)
        else:
            visible_cap = cv2.VideoCapture(self.visible_path)
            ir_cap = cv2.VideoCapture(self.ir_path)
        
        if not visible_cap.isOpened() or not ir_cap.isOpened():
            self.update_signal.emit({'error': '无法打开视频文件或摄像头'})
            return
        
        # 读取视频帧并处理
        while self.is_running:
            ret_vis, visible_frame = visible_cap.read()
            ret_ir, ir_frame = ir_cap.read()
            
            if not ret_vis or not ret_ir:
                self.update_signal.emit({'finished': True})
                break
                
            # 调整图像大小为相同尺寸
            height, width = min(visible_frame.shape[0], ir_frame.shape[0]), min(visible_frame.shape[1], ir_frame.shape[1])
            visible_frame = cv2.resize(visible_frame, (width, height))
            ir_frame = cv2.resize(ir_frame, (width, height))
            
            # 计算特征
            visible_features = self.feature_extractor.calculate_features(visible_frame, ir_frame)
            ir_features = self.feature_extractor.calculate_features(ir_frame, visible_frame)
            
            # 每10帧显示一次特征提取结果
            if self.frame_count % 10 == 0:
                edge_vis = self.feature_extractor.extract_edge_features(visible_frame)
                corner_vis = self.feature_extractor.extract_corner_features(visible_frame)
                frequency_vis = self.feature_extractor.extract_frequency_features(visible_frame)
                intensity_vis = self.feature_extractor.extract_intensity_curve(visible_frame)
                
                edge_ir = self.feature_extractor.extract_edge_features(ir_frame)
                corner_ir = self.feature_extractor.extract_corner_features(ir_frame)
                frequency_ir = self.feature_extractor.extract_frequency_features(ir_frame)
                intensity_ir = self.feature_extractor.extract_intensity_curve(ir_frame)
                
                # 发送特征结果
                self.update_signal.emit({
                    'frame_count': self.frame_count,
                    'visible_frame': visible_frame,
                    'ir_frame': ir_frame,
                    'visible_features': visible_features,
                    'ir_features': ir_features,
                    'edge_vis': edge_vis,
                    'corner_vis': corner_vis,
                    'frequency_vis': frequency_vis,
                    'intensity_vis': intensity_vis,
                    'edge_ir': edge_ir,
                    'corner_ir': corner_ir,
                    'frequency_ir': frequency_ir,
                    'intensity_ir': intensity_ir
                })
            else:
                # 只发送基本信息
                self.update_signal.emit({
                    'frame_count': self.frame_count,
                    'visible_frame': visible_frame,
                    'ir_frame': ir_frame,
                    'visible_features': visible_features,
                    'ir_features': ir_features
                })
            
            self.frame_count += 1
            self.msleep(30)  # 控制处理速度
            
        # 释放资源
        visible_cap.release()
        ir_cap.release()
        
    def stop(self):
        """停止线程"""
        self.is_running = False
        self.wait()

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        
        # 初始化特征提取器
        self.feature_extractor = FeatureExtractor()
        
        # 初始化数据存储
        self.all_features = []
        
        # 初始化特征可视化窗口
        self.feature_visualizer = FeatureVisualizer()
        
        # 初始化特征结果窗口
        self.feature_result_window = FeatureResultWindow()
        
        # 初始化视频处理线程
        self.video_processor = None
        
        # 探测体制权重配置
        self.weights = {
            '结构相似度': {'visible': 0.6, 'ir': 0.4},
            '信息熵': {'visible': 0.7, 'ir': 0.3},
            '平均灰度': {'visible': 0.5, 'ir': 0.5},
            '对比度': {'visible': 0.6, 'ir': 0.4},
            '高频复杂度': {'visible': 0.7, 'ir': 0.3},
            '能量': {'visible': 0.6, 'ir': 0.4}
        }
        
        # 最后初始化UI
        self.initUI()
        
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle('可见光与红外图像特征分析系统')
        self.resize(1200, 800)
        
        # 创建主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部控制区域
        control_layout = QHBoxLayout()
        
        # 左侧文件选择区域
        file_layout = QVBoxLayout()
        
        # 可见光文件选择
        visible_layout = QHBoxLayout()
        self.visible_label = QLabel('可见光文件:')
        self.visible_path = QLabel('未选择')
        self.visible_path.setStyleSheet("border: 1px solid #ccc; padding: 2px;")
        self.visible_btn = QPushButton('浏览...')
        self.visible_btn.clicked.connect(self.select_visible_file)
        visible_layout.addWidget(self.visible_label)
        visible_layout.addWidget(self.visible_path)
        visible_layout.addWidget(self.visible_btn)
        
        # 红外文件选择
        ir_layout = QHBoxLayout()
        self.ir_label = QLabel('红外文件:')
        self.ir_path = QLabel('未选择')
        self.ir_path.setStyleSheet("border: 1px solid #ccc; padding: 2px;")
        self.ir_btn = QPushButton('浏览...')
        self.ir_btn.clicked.connect(self.select_ir_file)
        ir_layout.addWidget(self.ir_label)
        ir_layout.addWidget(self.ir_path)
        ir_layout.addWidget(self.ir_btn)
        
        # 摄像头选项
        camera_layout = QHBoxLayout()
        self.use_camera = QCheckBox('使用摄像头')
        self.use_camera.stateChanged.connect(self.toggle_camera)
        self.camera_id = QSpinBox()
        self.camera_id.setRange(0, 10)
        self.camera_id.setValue(0)
        self.camera_id.setEnabled(False)
        camera_layout.addWidget(self.use_camera)
        camera_layout.addWidget(QLabel('摄像头ID:'))
        camera_layout.addWidget(self.camera_id)
        
        file_layout.addLayout(visible_layout)
        file_layout.addLayout(ir_layout)
        file_layout.addLayout(camera_layout)
        
        # 处理控制按钮
        control_buttons_layout = QVBoxLayout()
        
        # 处理按钮
        self.process_btn = QPushButton('开始处理')
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setMinimumHeight(40)
        
        # 保存特征按钮
        self.save_btn = QPushButton('保存特征数据')
        self.save_btn.clicked.connect(self.save_features)
        self.save_btn.setMinimumHeight(40)
        self.save_btn.setEnabled(False)
        
        # 显示特征结果按钮
        self.show_features_btn = QPushButton('显示特征结果')
        self.show_features_btn.clicked.connect(self.show_feature_results)
        self.show_features_btn.setMinimumHeight(40)
        self.show_features_btn.setEnabled(False)
        
        control_buttons_layout.addWidget(self.process_btn)
        control_buttons_layout.addWidget(self.save_btn)
        control_buttons_layout.addWidget(self.show_features_btn)
        
        control_layout.addLayout(file_layout, 3)
        control_layout.addLayout(control_buttons_layout, 1)
        
        # 中间显示区域
        display_layout = QHBoxLayout()
        
        # 左侧图像显示区域
        images_layout = QVBoxLayout()
        
        # 图像显示标题
        titles_layout = QHBoxLayout()
        titles_layout.addWidget(QLabel('可见光图像'))
        titles_layout.addWidget(QLabel('红外图像'))
        images_layout.addLayout(titles_layout)
        
        # 图像显示
        self.images_display = QHBoxLayout()
        self.visible_display = QLabel('等待图像...')
        self.visible_display.setAlignment(Qt.AlignCenter)
        self.visible_display.setStyleSheet("border: 1px solid #ccc;")
        
        self.ir_display = QLabel('等待图像...')
        self.ir_display.setAlignment(Qt.AlignCenter)
        self.ir_display.setStyleSheet("border: 1px solid #ccc;")
        
        self.images_display.addWidget(self.visible_display)
        self.images_display.addWidget(self.ir_display)
        
        images_layout.addLayout(self.images_display)
        
        # 场景判断结果
        self.scene_label = QLabel('场景判断: 未处理')
        self.scene_label.setAlignment(Qt.AlignCenter)
        self.scene_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-top: 10px;")
        images_layout.addWidget(self.scene_label)
        
        # 右侧特征显示区域
        features_layout = QVBoxLayout()
        features_title = QLabel('特征提取结果')
        features_title.setAlignment(Qt.AlignCenter)
        features_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        features_layout.addWidget(features_title)
        
        # 添加特征可视化窗口
        features_layout.addWidget(self.feature_visualizer)
        
        # 权重信息
        self.weight_info = QLabel('权重计算: 未处理')
        self.weight_info.setAlignment(Qt.AlignCenter)
        self.weight_info.setStyleSheet("font-size: 14px; margin-top: 10px;")
        features_layout.addWidget(self.weight_info)
        
        display_layout.addLayout(images_layout, 1)
        display_layout.addLayout(features_layout, 1)
        
        # 底部特征表格
        self.features_table = QWidget()
        self.features_table_layout = QGridLayout(self.features_table)
        
        # 添加特征名称行
        feature_names = ['帧号'] + self.feature_extractor.feature_names + ['可见光权重', '红外权重', '场景类型']
        for col, name in enumerate(feature_names):
            label = QLabel(name)
            label.setStyleSheet("font-weight: bold; border-bottom: 1px solid #ccc;")
            self.features_table_layout.addWidget(label, 0, col)
        
        # 滚动区域
        scroll_area = QWidget()
        scroll_layout = QVBoxLayout(scroll_area)
        scroll_layout.addWidget(self.features_table)
        scroll_layout.addStretch()
        
        from PyQt5.QtWidgets import QScrollArea
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(scroll_area)
        
        # 添加所有布局到主布局
        main_layout.addLayout(control_layout)
        main_layout.addLayout(display_layout, 3)
        main_layout.addWidget(scroll, 1)
        
    def select_visible_file(self):
        """选择可见光文件"""
        if self.use_camera.isChecked():
            self.visible_path.setText(f'摄像头 {self.camera_id.value()}')
            return
            
        file_path, _ = QFileDialog.getOpenFileName(self, '选择可见光文件', '', '图像视频文件 (*.jpg *.jpeg *.png *.bmp *.mp4 *.avi *.mkv);;所有文件 (*)')
        if file_path:
            self.visible_path.setText(file_path)
            
    def select_ir_file(self):
        """选择红外文件"""
        if self.use_camera.isChecked():
            self.ir_path.setText(f'摄像头 {self.camera_id.value() + 1}')
            return
            
        file_path, _ = QFileDialog.getOpenFileName(self, '选择红外文件', '', '图像视频文件 (*.jpg *.jpeg *.png *.bmp *.mp4 *.avi *.mkv);;所有文件 (*)')
        if file_path:
            self.ir_path.setText(file_path)
            
    def toggle_camera(self, state):
        """切换摄像头模式"""
        self.camera_id.setEnabled(state == Qt.Checked)
        if state == Qt.Checked:
            self.visible_path.setText(f'摄像头 {self.camera_id.value()}')
            self.ir_path.setText(f'摄像头 {self.camera_id.value() + 1}')
        else:
            self.visible_path.setText('未选择')
            self.ir_path.setText('未选择')
            
    def start_processing(self):
        """开始处理"""
        if self.process_btn.text() == '开始处理':
            # 获取文件路径
            visible_path = self.visible_path.text()
            ir_path = self.ir_path.text()
            
            if '未选择' in [visible_path, ir_path]:
                QMessageBox.warning(self, '警告', '请先选择可见光和红外文件!')
                return
                
            # 重置表格
            self.clear_features_table()
            
            # 开始处理
            self.process_btn.setText('停止处理')
            self.save_btn.setEnabled(False)
            self.show_features_btn.setEnabled(False)
            
            # 创建并启动视频处理线程
            self.video_processor = VideoProcessor(visible_path, ir_path, self.feature_extractor)
            self.video_processor.update_signal.connect(self.update_display)
            self.video_processor.start()
        else:
            # 停止处理
            if self.video_processor:
                self.video_processor.stop()
                self.video_processor = None
                
            self.process_btn.setText('开始处理')
            self.save_btn.setEnabled(True)
            self.show_features_btn.setEnabled(True)
            
    def update_display(self, data):
        """更新显示"""
        if 'error' in data:
            QMessageBox.critical(self, '错误', data['error'])
            self.process_btn.setText('开始处理')
            return
            
        if 'finished' in data:
            QMessageBox.information(self, '完成', '视频处理完成!')
            self.process_btn.setText('开始处理')
            self.save_btn.setEnabled(True)
            self.show_features_btn.setEnabled(True)
            return
            
        # 更新图像显示
        self.update_image_display(self.visible_display, data['visible_frame'])
        self.update_image_display(self.ir_display, data['ir_frame'])
        
        # 获取特征
        visible_features = data['visible_features']
        ir_features = data['ir_features']
        
        # 计算总权重
        visible_weight, ir_weight = self.calculate_weights(visible_features, ir_features)
        
        # 场景判断
        scene_type = self.judge_scene(visible_features, ir_features)
        
        # 更新场景标签
        self.scene_label.setText(f'场景判断: {scene_type}')
        
        # 更新权重信息
        self.weight_info.setText(f'可见光权重: {visible_weight:.2f}, 红外权重: {ir_weight:.2f}')
        
        # 更新特征可视化
        feature_names = self.feature_extractor.feature_names
        visible_feature_values = [visible_features[name] for name in feature_names]
        ir_feature_values = [ir_features[name] for name in feature_names]
        
        self.feature_visualizer.update_feature_bar_chart(visible_feature_values, ir_feature_values, feature_names)
        self.feature_visualizer.update_weight_pie_chart(visible_weight, ir_weight)
        
        # 保存特征数据
        self.all_features.append({
            'frame': data['frame_count'],
            'visible_features': visible_features,
            'ir_features': ir_features,
            'visible_weight': visible_weight,
            'ir_weight': ir_weight,
            'scene_type': scene_type
        })
        
        # 更新特征表格
        self.update_features_table(data['frame_count'], visible_features, ir_features, visible_weight, ir_weight, scene_type)
        
        # 每10帧更新一次特征结果窗口
        if 'edge_vis' in data:
            self.feature_result_window.update_features(
                data['visible_frame'], data['edge_vis'], data['corner_vis'], 
                data['frequency_vis'], data['intensity_vis']
            )
            self.show_features_btn.setEnabled(True)
        
    def update_image_display(self, label, img):
        """更新图像显示"""
        if len(img.shape) == 3:
            height, width, channel = img.shape
            bytesPerLine = 3 * width
            qImg = QImage(img.data, width, height, bytesPerLine, QImage.Format_RGB888).rgbSwapped()
        else:
            height, width = img.shape
            qImg = QImage(img.data, width, height, width, QImage.Format_Grayscale8)
            
        pixmap = QPixmap.fromImage(qImg)
        label.setPixmap(pixmap.scaled(label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
    def calculate_weights(self, visible_features, ir_features):
        """计算可见光和红外的总权重"""
        visible_total = 0
        ir_total = 0
        
        for feature_name in self.feature_extractor.feature_names:
            visible_val = visible_features[feature_name]
            ir_val = ir_features[feature_name]
            
            # 根据特征权重计算
            visible_weight = self.weights[feature_name]['visible']
            ir_weight = self.weights[feature_name]['ir']
            
            visible_total += visible_val * visible_weight
            ir_total += ir_val * ir_weight
            
        # 归一化
        total = visible_total + ir_total
        if total == 0:
            return 0.5, 0.5
            
        return visible_total / total, ir_total / total
        
    def judge_scene(self, visible_features, ir_features):
        """判断场景类型（白天/黑夜）"""
        # 基于灰度值的判断
        visible_gray = visible_features['平均灰度']
        ir_gray = ir_features['平均灰度']
        
        # 基于对比度的判断
        visible_contrast = visible_features['对比度']
        ir_contrast = ir_features['对比度']
        
        # 白天场景：可见光图像灰度值高，对比度高
        # 黑夜场景：红外图像灰度值高，对比度高
        if visible_gray > ir_gray and visible_contrast > ir_contrast:
            return '白天'
        elif ir_gray > visible_gray and ir_contrast > visible_contrast:
            return '黑夜'
        else:
            # 综合判断
            visible_score = visible_gray * 0.6 + visible_contrast * 0.4
            ir_score = ir_gray * 0.6 + ir_contrast * 0.4
            
            return '白天' if visible_score > ir_score else '黑夜'
        
    def update_features_table(self, frame_count, visible_features, ir_features, visible_weight, ir_weight, scene_type):
        """更新特征表格"""
        # 获取当前行数
        row = self.features_table_layout.rowCount()
        
        # 添加帧号
        self.features_table_layout.addWidget(QLabel(str(frame_count)), row, 0)
        
        # 添加特征值
        col = 1
        for feature_name in self.feature_extractor.feature_names:
            # 可见光特征
            self.features_table_layout.addWidget(QLabel(f"{visible_features[feature_name]:.4f}"), row, col)
            # 红外特征
            self.features_table_layout.addWidget(QLabel(f"{ir_features[feature_name]:.4f}"), row, col + 1)
            col += 2
            
        # 添加权重
        self.features_table_layout.addWidget(QLabel(f"{visible_weight:.4f}"), row, col)
        self.features_table_layout.addWidget(QLabel(f"{ir_weight:.4f}"), row, col + 1)
        
        # 添加场景类型
        self.features_table_layout.addWidget(QLabel(scene_type), row, col + 2)
        
    def clear_features_table(self):
        """清除特征表格"""
        # 保留标题行
        while self.features_table_layout.rowCount() > 1:
            for col in range(self.features_table_layout.columnCount()):
                item = self.features_table_layout.itemAtPosition(self.features_table_layout.rowCount() - 1, col)
                if item:
                    widget = item.widget()
                    if widget:
                        self.features_table_layout.removeWidget(widget)
                        widget.deleteLater()
            self.features_table_layout.setRowMinimumHeight(self.features_table_layout.rowCount() - 1, 0)
            self.features_table_layout.removeRow(self.features_table_layout.rowCount() - 1)
            
    def save_features(self):
        """保存特征数据到CSV文件"""
        if not self.all_features:
            QMessageBox.warning(self, '警告', '没有特征数据可保存!')
            return
            
        file_path, _ = QFileDialog.getSaveFileName(self, '保存特征数据', '', 'CSV文件 (*.csv);;所有文件 (*)')
        if not file_path:
            return
            
        try:
            # 准备数据
            data = []
            for item in self.all_features:
                row = {
                    'frame': item['frame'],
                    'scene_type': item['scene_type'],
                    'visible_weight': item['visible_weight'],
                    'ir_weight': item['ir_weight']
                }
                
                # 添加可见光特征
                for feature_name, value in item['visible_features'].items():
                    row[f'visible_{feature_name}'] = value
                    
                # 添加红外特征
                for feature_name, value in item['ir_features'].items():
                    row[f'ir_{feature_name}'] = value
                    
                data.append(row)
                
            # 创建DataFrame并保存
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False)
            
            QMessageBox.information(self, '成功', f'特征数据已保存到 {file_path}')
        except Exception as e:
            QMessageBox.critical(self, '错误', f'保存特征数据时出错: {str(e)}')
            
    def show_feature_results(self):
        """显示特征结果窗口"""
        self.feature_result_window.show()
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.video_processor and self.video_processor.isRunning():
            self.video_processor.stop()
            
        event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())    