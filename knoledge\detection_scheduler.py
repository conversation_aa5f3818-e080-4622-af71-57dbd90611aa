import numpy as np

class DetectionScheduler:
    def __init__(self, config):
        self.config = config
        self.scene_types = config['scene_types']
        
        # 初始化场景-探测体制权重映射
        self.scene_weight_map = self._init_scene_weight_map()
    
    def _init_scene_weight_map(self):
        """初始化场景与探测体制的权重映射"""
        # 不同场景下各探测体制的基础权重
        return {
            'sunny': {'visible': 0.6, 'infrared': 0.3, 'sar': 0.1},
            'rainy': {'visible': 0.2, 'infrared': 0.5, 'sar': 0.3},
            'night': {'visible': 0.1, 'infrared': 0.7, 'sar': 0.2},
            'foggy': {'visible': 0.1, 'infrared': 0.4, 'sar': 0.5}
        }
    
    def calculate_detection_weights(self, feature_metrics, scene_type, feature_sensitivity):
        """计算各探测体制的动态权重"""
        # 获取场景基础权重
        base_weights = self.scene_weight_map.get(scene_type, {'visible': 0.4, 'infrared': 0.3, 'sar': 0.3})
        
        # 基于特征质量调整权重
        adjusted_weights = {}
        for sensor_type in base_weights:
            # 结合特征评估指标和场景敏感性
            feature_weight = feature_metrics[sensor_type]['weight']
            sensitivity = feature_sensitivity[sensor_type]
            
            # 计算调整后的权重
            adjusted_weights[sensor_type] = base_weights[sensor_type] * feature_weight * sensitivity
        
        # 归一化权重
        total_weight = sum(adjusted_weights.values())
        normalized_weights = {k: v / total_weight for k, v in adjusted_weights.items()}
        
        return normalized_weights
    
    def schedule_features(self, features, detection_weights):
        """基于权重调度特征"""
        # 特征融合
        fused_feature = None
        
        for sensor_type, weight in detection_weights.items():
            if sensor_type in features:
                feature = features[sensor_type]
                if fused_feature is None:
                    fused_feature = feature * weight
                else:
                    fused_feature += feature * weight
        
        # 归一化融合特征
        if fused_feature is not None:
            norm = np.linalg.norm(fused_feature)
            if norm > 0:
                fused_feature = fused_feature / norm
        
        return fused_feature    