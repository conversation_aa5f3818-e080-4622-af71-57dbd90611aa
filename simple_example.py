"""
图像分析系统简单使用示例
"""

import cv2
import numpy as np
from image_analysis_system import ImageAnalysisSystem

def simple_example():
    """简单使用示例"""
    print("=== 图像分析系统简单示例 ===")
    
    # 1. 初始化系统
    system = ImageAnalysisSystem()
    
    # 2. 使用现有图像或创建示例图像
    image_path = "visible/000000.jpg"
    
    # 如果图像不存在，创建一个示例图像
    if not cv2.imread(image_path) is not None:
        demo_image = create_simple_demo_image()
        image_path = "simple_demo.jpg"
        cv2.imwrite(image_path, demo_image)
        print(f"创建了示例图像: {image_path}")
    
    # 3. 设置分析参数
    scene_description = "晴天"  # 场景描述
    individual_bbox = (100, 100, 200, 150)  # 个体边界框 (x, y, width, height)
    component_bbox = (150, 125, 80, 60)     # 部件边界框
    entity_type = "电科一号"                 # 个体类型
    component_name = "雷达"                  # 部件名称
    
    # 4. 执行分析
    print(f"正在分析图像: {image_path}")
    results = system.process_image(
        image_path=image_path,
        scene_description=scene_description,
        individual_bbox=individual_bbox,
        component_bbox=component_bbox,
        entity_type=entity_type,
        component_name=component_name
    )
    
    # 5. 显示关键结果
    print("\n=== 分析结果 ===")
    
    # 场景匹配结果
    scene_info = results['scene_comparison']
    print(f"场景: {scene_info['scene_description']}")
    print(f"匹配度: {scene_info['match_score']:.3f}")
    print(f"匹配状态: {'✓ 匹配' if scene_info['is_match'] else '✗ 不匹配'}")
    
    # 几何特征
    geo_features = results['geometric_features']
    print(f"\n几何特征:")
    print(f"  长宽比: {geo_features['aspect_ratio']:.3f}")
    print(f"  圆度: {geo_features['circularity']:.3f}")
    print(f"  高热区域位置: ({geo_features['hot_region_position'][0]:.3f}, {geo_features['hot_region_position'][1]:.3f})")
    print(f"  背景对比度: {geo_features['background_contrast']:.1f}")
    
    # 部件关系
    comp_relations = results['component_relations']
    print(f"\n部件关系:")
    print(f"  {comp_relations['component_name']} 属于 {comp_relations['entity_type']}: {'是' if comp_relations['belongs_to_entity'] else '否'}")
    
    if comp_relations['related_components']:
        print(f"  相关部件:")
        for comp in comp_relations['related_components']:
            print(f"    - {comp['component']}: {comp['relation']}")
    
    # 6. 保存结果
    output_path = "simple_analysis_result.jpg"
    cv2.imwrite(output_path, results['result_image'])
    print(f"\n结果图像已保存到: {output_path}")
    
    return results

def create_simple_demo_image():
    """创建简单的演示图像"""
    # 创建640x480的图像
    image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加渐变背景（模拟晴天）
    for i in range(480):
        for j in range(640):
            # 蓝天渐变效果
            image[i, j] = [
                min(255, 100 + i // 3),      # B
                min(255, 150 + i // 4),      # G  
                min(255, 200 + i // 6)       # R
            ]
    
    # 添加个体目标（矩形区域）
    cv2.rectangle(image, (100, 100), (300, 250), (60, 100, 140), -1)  # 填充
    cv2.rectangle(image, (100, 100), (300, 250), (0, 255, 0), 3)      # 边框
    
    # 添加部件目标（圆形区域）
    cv2.circle(image, (200, 175), 40, (180, 80, 80), -1)   # 填充
    cv2.circle(image, (200, 175), 40, (255, 0, 0), 3)      # 边框
    
    # 添加一些细节
    cv2.rectangle(image, (180, 155), (220, 195), (120, 120, 120), -1)
    
    return image

def batch_analysis_example():
    """批量分析示例"""
    print("\n=== 批量分析示例 ===")
    
    system = ImageAnalysisSystem()
    
    # 创建多个测试场景
    test_scenarios = [
        {"scene": "晴天", "bbox": (100, 100, 200, 150)},
        {"scene": "雨天", "bbox": (120, 120, 180, 140)},
        {"scene": "夜晚", "bbox": (80, 80, 240, 180)}
    ]
    
    results_summary = []
    
    for i, scenario in enumerate(test_scenarios):
        # 创建测试图像
        if scenario["scene"] == "晴天":
            test_image = create_simple_demo_image()
        elif scenario["scene"] == "雨天":
            test_image = create_rainy_demo()
        else:  # 夜晚
            test_image = create_night_demo()
        
        image_path = f"batch_test_{i+1}_{scenario['scene']}.jpg"
        cv2.imwrite(image_path, test_image)
        
        # 分析图像
        try:
            results = system.process_image(
                image_path=image_path,
                scene_description=scenario["scene"],
                individual_bbox=scenario["bbox"],
                component_bbox=(scenario["bbox"][0]+50, scenario["bbox"][1]+25, 80, 60)
            )
            
            # 记录结果
            summary = {
                "场景": scenario["scene"],
                "匹配度": results['scene_comparison']['match_score'],
                "长宽比": results['geometric_features']['aspect_ratio'],
                "圆度": results['geometric_features']['circularity']
            }
            results_summary.append(summary)
            
            print(f"{scenario['scene']}场景 - 匹配度: {summary['匹配度']:.3f}")
            
        except Exception as e:
            print(f"{scenario['scene']}场景分析失败: {e}")
    
    # 显示汇总结果
    print(f"\n批量分析完成，共处理 {len(results_summary)} 个场景")
    for summary in results_summary:
        print(f"  {summary['场景']}: 匹配度={summary['匹配度']:.3f}, 长宽比={summary['长宽比']:.3f}")

def create_rainy_demo():
    """创建雨天演示图像"""
    image = np.ones((480, 640, 3), dtype=np.uint8) * 100
    
    # 添加雨滴
    for _ in range(500):
        x, y = np.random.randint(0, 640), np.random.randint(0, 480)
        cv2.line(image, (x, y), (x+1, y+5), (180, 180, 180), 1)
    
    # 添加目标
    cv2.rectangle(image, (120, 120), (300, 260), (50, 80, 120), -1)
    cv2.circle(image, (210, 190), 35, (150, 70, 70), -1)
    
    return image

def create_night_demo():
    """创建夜晚演示图像"""
    image = np.ones((480, 640, 3), dtype=np.uint8) * 30
    
    # 添加星星
    for _ in range(50):
        x, y = np.random.randint(0, 640), np.random.randint(0, 240)
        cv2.circle(image, (x, y), 1, (255, 255, 200), -1)
    
    # 添加目标（更亮）
    cv2.rectangle(image, (80, 80), (320, 260), (100, 120, 160), -1)
    cv2.circle(image, (200, 170), 45, (200, 100, 100), -1)
    
    return image

if __name__ == "__main__":
    # 运行简单示例
    simple_example()
    
    # 运行批量分析示例
    batch_analysis_example()
    
    print("\n=== 示例运行完成 ===")
    print("查看生成的图像文件以了解分析结果！")
