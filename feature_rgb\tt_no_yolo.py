import sys
import os
import random
import numpy as np
import cv2
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFileDialog, QSlider, 
                            QGroupBox, QGridLayout, QTextEdit, QComboBox, QSplitter)
from PyQt5.QtGui import QPixmap, QImage, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from typing import List, Dict, Tuple

class FeatureExtractor:
    """特征提取器，用于从不同类型的图像中提取特征"""
    
    @staticmethod
    def extract_visible_features(image: np.ndarray) -> Dict:
        """从可见光图像中提取特征"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算亮度统计
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.count_nonzero(edges) / (edges.shape[0] * edges.shape[1])
        
        # 颜色特征
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        mean_hue = np.mean(hsv[:, :, 0])
        mean_saturation = np.mean(hsv[:, :, 1])
        
        return {
            'mean_brightness': mean_brightness,
            'std_brightness': std_brightness,
            'edge_density': edge_density,
            'mean_hue': mean_hue,
            'mean_saturation': mean_saturation,
            'feature_vector': [mean_brightness, std_brightness, edge_density, mean_hue, mean_saturation]
        }

class ImageWidget(QWidget):
    """用于显示图像的自定义控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image = None
        self.title = ""
        
        self.initUI()
    
    def initUI(self):
        self.layout = QVBoxLayout(self)
        
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(200, 200)
        self.layout.addWidget(self.image_label)
        
        self.setLayout(self.layout)
    
    def setTitle(self, title: str):
        """设置标题"""
        self.title = title
        self.title_label.setText(title)
    
    def set_image(self, image: np.ndarray, title: str):
        """设置要显示的图像"""
        self.image = image.copy()
        self.title = title
        
        self.update_image()
    
    def update_image(self):
        """更新显示的图像"""
        if self.image is None:
            return
        
        # 更新标题
        self.title_label.setText(self.title)
        
        # 转换为Qt格式并显示
        height, width = self.image.shape[:2]
        if len(self.image.shape) == 3:
            channels = self.image.shape[2]
            bytesPerLine = channels * width
            qImg = QImage(self.image.data, width, height, bytesPerLine, QImage.Format_BGR888)
        else:
            bytesPerLine = width
            qImg = QImage(self.image.data, width, height, bytesPerLine, QImage.Format_Grayscale8)
            
        self.image_label.setPixmap(QPixmap.fromImage(qImg).scaled(
            300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation))

class SimpleMultiModalApp(QMainWindow):
    """简化的多模态图像识别应用"""
    
    def __init__(self):
        super().__init__()
        print("正在初始化简化版多模态识别应用...")
        
        self.visible_image = None
        self.thermal_image = None
        self.sar_image = None
        
        self.initUI()
        print("应用程序初始化完成")
    
    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle("简化版多模态图像识别系统")
        self.setGeometry(100, 100, 1000, 600)
        
        # 创建中央部件和主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建图像选择区域
        selection_layout = QHBoxLayout()
        
        # 可见光图像选择
        visible_group = QGroupBox("可见光图像")
        visible_layout = QVBoxLayout(visible_group)
        
        self.visible_path_label = QLabel("未选择图像")
        visible_layout.addWidget(self.visible_path_label)
        
        self.visible_select_button = QPushButton("选择图像")
        self.visible_select_button.clicked.connect(self.select_visible_image)
        visible_layout.addWidget(self.visible_select_button)
        
        selection_layout.addWidget(visible_group)
        
        # 红外图像选择
        thermal_group = QGroupBox("红外图像")
        thermal_layout = QVBoxLayout(thermal_group)
        
        self.thermal_path_label = QLabel("未选择图像")
        thermal_layout.addWidget(self.thermal_path_label)
        
        self.thermal_select_button = QPushButton("选择图像")
        self.thermal_select_button.clicked.connect(self.select_thermal_image)
        thermal_layout.addWidget(self.thermal_select_button)
        
        selection_layout.addWidget(thermal_group)
        
        # SAR图像选择
        sar_group = QGroupBox("SAR图像")
        sar_layout = QVBoxLayout(sar_group)
        
        self.sar_path_label = QLabel("未选择图像")
        sar_layout.addWidget(self.sar_path_label)
        
        self.sar_select_button = QPushButton("选择图像")
        self.sar_select_button.clicked.connect(self.select_sar_image)
        sar_layout.addWidget(self.sar_select_button)
        
        selection_layout.addWidget(sar_group)
        
        main_layout.addLayout(selection_layout)
        
        # 创建图像显示区域
        image_display_layout = QHBoxLayout()
        
        self.visible_image_widget = ImageWidget()
        self.visible_image_widget.setTitle("可见光图像")
        image_display_layout.addWidget(self.visible_image_widget)
        
        self.thermal_image_widget = ImageWidget()
        self.thermal_image_widget.setTitle("红外图像")
        image_display_layout.addWidget(self.thermal_image_widget)
        
        self.sar_image_widget = ImageWidget()
        self.sar_image_widget.setTitle("SAR图像")
        image_display_layout.addWidget(self.sar_image_widget)
        
        main_layout.addLayout(image_display_layout)
        
        # 创建特征提取按钮
        self.feature_extract_button = QPushButton("提取特征")
        self.feature_extract_button.clicked.connect(self.extract_features)
        self.feature_extract_button.setEnabled(False)
        main_layout.addWidget(self.feature_extract_button)
        
        # 创建结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMaximumHeight(150)
        main_layout.addWidget(self.result_text)
    
    def select_visible_image(self):
        """选择可见光图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择可见光图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)", options=options)
        
        if file_name:
            self.visible_image_path = file_name
            self.visible_path_label.setText(os.path.basename(file_name))
            
            # 加载并显示图像
            self.visible_image = cv2.imread(file_name)
            if self.visible_image is not None:
                self.visible_image_widget.set_image(self.visible_image, "可见光图像")
                self.check_all_images_selected()
            else:
                print(f"无法加载图像: {file_name}")
    
    def select_thermal_image(self):
        """选择红外图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择红外图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)", options=options)
        
        if file_name:
            self.thermal_image_path = file_name
            self.thermal_path_label.setText(os.path.basename(file_name))
            
            # 加载并显示图像
            self.thermal_image = cv2.imread(file_name)
            if self.thermal_image is not None:
                self.thermal_image_widget.set_image(self.thermal_image, "红外图像")
                self.check_all_images_selected()
            else:
                print(f"无法加载图像: {file_name}")
    
    def select_sar_image(self):
        """选择SAR图像"""
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择SAR图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*)", options=options)
        
        if file_name:
            self.sar_image_path = file_name
            self.sar_path_label.setText(os.path.basename(file_name))
            
            # 加载并显示图像
            self.sar_image = cv2.imread(file_name)
            if self.sar_image is not None:
                self.sar_image_widget.set_image(self.sar_image, "SAR图像")
                self.check_all_images_selected()
            else:
                print(f"无法加载图像: {file_name}")
    
    def check_all_images_selected(self):
        """检查是否所有类型的图像都已选择"""
        if (hasattr(self, 'visible_image') and self.visible_image is not None and
            hasattr(self, 'thermal_image') and self.thermal_image is not None and
            hasattr(self, 'sar_image') and self.sar_image is not None):
            self.feature_extract_button.setEnabled(True)
    
    def extract_features(self):
        """提取特征"""
        if self.visible_image is not None:
            try:
                feature_extractor = FeatureExtractor()
                features = feature_extractor.extract_visible_features(self.visible_image)
                
                result_text = "特征提取完成！\n\n"
                result_text += f"可见光图像特征:\n"
                result_text += f"平均亮度: {features['mean_brightness']:.2f}\n"
                result_text += f"亮度标准差: {features['std_brightness']:.2f}\n"
                result_text += f"边缘密度: {features['edge_density']:.4f}\n"
                result_text += f"平均色调: {features['mean_hue']:.2f}\n"
                result_text += f"平均饱和度: {features['mean_saturation']:.2f}\n"
                
                self.result_text.setText(result_text)
                print("特征提取成功完成")
                
            except Exception as e:
                error_text = f"特征提取时出错: {e}"
                self.result_text.setText(error_text)
                print(error_text)

if __name__ == "__main__":
    print("正在启动简化版多模态图像识别系统...")
    app = QApplication(sys.argv)
    print("QApplication创建成功")
    
    app.setStyle('Fusion')
    print("应用样式设置完成")
    
    print("正在创建主窗口...")
    window = SimpleMultiModalApp()
    print("正在显示窗口...")
    window.show()
    print("进入事件循环...")
    sys.exit(app.exec_())
