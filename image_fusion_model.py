import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import transforms
from PIL import Image
import os

class ConvBlock(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super(ConvBlock, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        return self.relu(self.conv(x))

class MultiScaleFusionNet(nn.Module):
    def __init__(self, num_channels=64):
        super(MultiScaleFusionNet, self).__init__()
        
        # 特征提取网络 - 共享权重
        self.feature_extractor = nn.Sequential(
            ConvBlock(1, num_channels),
            ConvBlock(num_channels, num_channels)
        )
        
        # 多尺度处理 - 不同大小的卷积核
        self.scale1 = ConvBlock(num_channels, num_channels, kernel_size=3)
        self.scale2 = ConvBlock(num_channels, num_channels, kernel_size=5, padding=2)
        self.scale3 = ConvBlock(num_channels, num_channels, kernel_size=7, padding=3)
        
        # 注意力融合模块
        self.attention = nn.Sequential(
            ConvBlock(num_channels*3, num_channels),
            nn.Conv2d(num_channels, 3, kernel_size=1),
            nn.Softmax(dim=1)
        )
        
        # 重构网络
        self.reconstruction = nn.Sequential(
            ConvBlock(num_channels, num_channels),
            nn.Conv2d(num_channels, 1, kernel_size=3, padding=1)
        )

    def forward(self, visible, infrared):
        # 特征提取
        v_features = self.feature_extractor(visible)
        i_features = self.feature_extractor(infrared)
        
        # 计算特征相似性图
        similarity_map = torch.sigmoid(torch.abs(v_features - i_features))
        
        # 多尺度特征提取
        v_scale1 = self.scale1(v_features)
        v_scale2 = self.scale2(v_features)
        v_scale3 = self.scale3(v_features)
        
        i_scale1 = self.scale1(i_features)
        i_scale2 = self.scale2(i_features)
        i_scale3 = self.scale3(i_features)
        
        # 基于相似性的自适应融合策略
        v_multi_scale = torch.cat([v_scale1, v_scale2, v_scale3], dim=1)
        i_multi_scale = torch.cat([i_scale1, i_scale2, i_scale3], dim=1)
        
        # 计算注意力权重
        v_attention = self.attention(v_multi_scale)
        i_attention = self.attention(i_multi_scale)
        
        # 应用注意力权重
        v_scale1_weighted = v_scale1 * v_attention[:, 0:1, :, :]
        v_scale2_weighted = v_scale2 * v_attention[:, 1:2, :, :]
        v_scale3_weighted = v_scale3 * v_attention[:, 2:3, :, :]
        
        i_scale1_weighted = i_scale1 * i_attention[:, 0:1, :, :]
        i_scale2_weighted = i_scale2 * i_attention[:, 1:2, :, :]
        i_scale3_weighted = i_scale3 * i_attention[:, 2:3, :, :]
        
        # 特征融合
        fused_features = (v_scale1_weighted + i_scale1_weighted) * similarity_map + \
                         (v_scale2_weighted + i_scale2_weighted) * (1 - similarity_map) + \
                         (v_scale3_weighted + i_scale3_weighted)
        
        # 图像重构
        fused_image = self.reconstruction(fused_features)
        return torch.tanh(fused_image)

class UnsupervisedFusionTrainer:
    def __init__(self, model, device, lr=1e-4):
        self.model = model.to(device)
        self.device = device
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        
        # 定义损失函数组件
        self.l1_loss = nn.L1Loss()
        
    def gradient_loss(self, img1, img2):
        # 计算梯度
        kernel_x = torch.Tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]).view(1, 1, 3, 3).to(self.device)
        kernel_y = torch.Tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]).view(1, 1, 3, 3).to(self.device)
        
        grad_x1 = F.conv2d(img1, kernel_x, padding=1)
        grad_y1 = F.conv2d(img1, kernel_y, padding=1)
        grad1 = torch.sqrt(grad_x1**2 + grad_y1**2 + 1e-6)
        
        grad_x2 = F.conv2d(img2, kernel_x, padding=1)
        grad_y2 = F.conv2d(img2, kernel_y, padding=1)
        grad2 = torch.sqrt(grad_x2**2 + grad_y2**2 + 1e-6)
        
        return self.l1_loss(grad1, grad2)
    
    def train_step(self, visible_batch, infrared_batch):
        self.model.train()
        self.optimizer.zero_grad()
        
        # 前向传播
        fused_images = self.model(visible_batch, infrared_batch)
        
        # 计算损失
        # 1. 保留可见光图像的强度信息
        intensity_loss = self.l1_loss(fused_images, visible_batch)
        
        # 2. 保留红外图像的梯度/结构信息
        structure_loss = self.gradient_loss(fused_images, infrared_batch)
        
        # 3. 总变差正则化，保证图像平滑性
        tv_loss = torch.mean(torch.abs(fused_images[:, :, :, :-1] - fused_images[:, :, :, 1:])) + \
                  torch.mean(torch.abs(fused_images[:, :, :-1, :] - fused_images[:, :, 1:, :]))
        
        # 总损失
        total_loss = intensity_loss + 0.8 * structure_loss + 0.01 * tv_loss
        
        # 反向传播和优化
        total_loss.backward()
        self.optimizer.step()
        
        return total_loss.item()
    
    def save_model(self, path):
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
        }, path)
    
    def load_model(self, path):
        checkpoint = torch.load(path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

def preprocess_image(image_path, size=None):
    """加载并预处理图像"""
    img = Image.open(image_path).convert('L')  # 转换为灰度图
    if size:
        img = img.resize(size, Image.BICUBIC)
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize([0.5], [0.5])  # 归一化到[-1, 1]
    ])
    return transform(img).unsqueeze(0)  # 添加batch维度

def postprocess_image(tensor):
    """将处理后的tensor转换回PIL图像"""
    img = tensor.squeeze(0).cpu()  # 移除batch维度并移到CPU
    img = img * 0.5 + 0.5  # 反归一化到[0, 1]
    img = transforms.ToPILImage()(img)
    return img

def train_model(train_loader, model, device, epochs=100, save_interval=10, save_dir='models'):
    """训练模型的主函数"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    trainer = UnsupervisedFusionTrainer(model, device)
    
    for epoch in range(epochs):
        total_loss = 0.0
        batch_count = 0
        
        for visible_batch, infrared_batch in train_loader:
            visible_batch = visible_batch.to(device)
            infrared_batch = infrared_batch.to(device)
            
            loss = trainer.train_step(visible_batch, infrared_batch)
            total_loss += loss
            batch_count += 1
        
        avg_loss = total_loss / batch_count
        print(f'Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}')
        
        if (epoch + 1) % save_interval == 0:
            model_path = os.path.join(save_dir, f'model_epoch_{epoch+1}.pth')
            trainer.save_model(model_path)
            print(f'Model saved to {model_path}')
    
    # 保存最终模型
    final_model_path = os.path.join(save_dir, 'final_model.pth')
    trainer.save_model(final_model_path)
    print(f'Final model saved to {final_model_path}')
    
    return trainer

def fuse_images(visible_path, infrared_path, model, device, output_path=None):
    """融合单对可见光和红外图像"""
    model.eval()
    
    # 预处理图像
    visible_img = preprocess_image(visible_path).to(device)
    infrared_img = preprocess_image(infrared_path, size=visible_img.shape[2:]).to(device)
    #把红外图像调整为与可见光图像相同的大小
    if infrared_img.shape[2:] != visible_img.shape[2:]:
        infrared_img = F.interpolate(infrared_img, size=visible_img.shape[2:], mode='bicubic', align_corners=False)
    # 融合图像
    with torch.no_grad():
        fused_img = model(visible_img, infrared_img)
    
    # 后处理并保存结果
    fused_pil = postprocess_image(fused_img)
    
    if output_path:
        fused_pil.save(output_path)
        print(f'Fused image saved to {output_path}')
    
    return fused_pil    