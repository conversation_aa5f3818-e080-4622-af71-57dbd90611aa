"""
图像分析系统
功能：场景识别、特征提取、几何特征分析、部件查询和可视化
"""

import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
import os
import json
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class ImageAnalysisSystem:
    """图像分析系统主类"""
    
    def __init__(self, csv_file_path: str = "benti/diankeyihao.csv"):
        """
        初始化系统
        
        Args:
            csv_file_path: 部件关系CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.scaler = StandardScaler()
        self.component_relations = self._load_component_relations()
        
    def _load_component_relations(self) -> pd.DataFrame:
        """加载部件关系数据"""
        try:
            if os.path.exists(self.csv_file_path):
                return pd.read_csv(self.csv_file_path)
            else:
                print(f"警告：CSV文件 {self.csv_file_path} 不存在")
                return pd.DataFrame()
        except Exception as e:
            print(f"加载CSV文件时出错：{e}")
            return pd.DataFrame()
    
    def extract_image_features(self, image: np.ndarray) -> Dict:
        """
        提取图像特征
        
        Args:
            image: 输入图像
            
        Returns:
            特征字典
        """
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 计算基本统计特征
        features = {
            'mean_intensity': np.mean(gray),
            'std_intensity': np.std(gray),
            'entropy': self._calculate_entropy(gray),
            'contrast': self._calculate_contrast(gray),
            'edge_density': self._calculate_edge_density(gray),
            'structure_complexity': self._calculate_structure_complexity(gray)
        }
        
        return features
    
    def _calculate_entropy(self, image: np.ndarray) -> float:
        """计算图像熵"""
        hist = cv2.calcHist([image], [0], None, [256], [0, 256])
        hist = hist.flatten()
        hist = hist[hist > 0]  # 移除零值
        prob = hist / hist.sum()
        entropy = -np.sum(prob * np.log2(prob))
        return entropy
    
    def _calculate_contrast(self, image: np.ndarray) -> float:
        """计算图像对比度"""
        return np.std(image)
    
    def _calculate_edge_density(self, image: np.ndarray) -> float:
        """计算边缘密度"""
        edges = cv2.Canny(image, 50, 150)
        return np.sum(edges > 0) / (image.shape[0] * image.shape[1])
    
    def _calculate_structure_complexity(self, image: np.ndarray) -> float:
        """计算结构复杂度"""
        # 使用Sobel算子计算梯度
        grad_x = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        return np.mean(magnitude)
    
    def normalize_features(self, features: Dict) -> np.ndarray:
        """
        归一化特征
        
        Args:
            features: 特征字典
            
        Returns:
            归一化后的特征向量
        """
        feature_vector = np.array(list(features.values())).reshape(1, -1)
        normalized = self.scaler.fit_transform(feature_vector)
        return normalized.flatten()
    
    def compare_with_scene(self, current_features: Dict, scene_description: str) -> Dict:
        """
        与场景识别结果进行对比
        
        Args:
            current_features: 当前图像特征
            scene_description: 场景描述
            
        Returns:
            对比结果
        """
        # 根据场景描述设定期望特征范围
        scene_expectations = self._get_scene_expectations(scene_description)
        
        # 计算匹配度
        match_score = self._calculate_scene_match(current_features, scene_expectations)
        
        result = {
            'scene_description': scene_description,
            'match_score': match_score,
            'is_match': match_score > 0.7,  # 阈值可调整
            'current_features': current_features,
            'expected_features': scene_expectations
        }
        
        return result
    
    def _get_scene_expectations(self, scene_description: str) -> Dict:
        """根据场景描述获取期望特征"""
        # 预定义不同场景的特征期望值
        scene_templates = {
            '晴天': {
                'mean_intensity': (120, 200),
                'contrast': (20, 50),
                'entropy': (4.0, 6.0),
                'edge_density': (0.01, 0.05)
            },
            '雨天': {
                'mean_intensity': (80, 150),
                'contrast': (15, 40),
                'entropy': (3.5, 5.5),
                'edge_density': (0.005, 0.03)
            },
            '雾天': {
                'mean_intensity': (100, 180),
                'contrast': (10, 30),
                'entropy': (3.0, 5.0),
                'edge_density': (0.003, 0.02)
            },
            '夜晚': {
                'mean_intensity': (30, 100),
                'contrast': (25, 60),
                'entropy': (4.5, 7.0),
                'edge_density': (0.01, 0.04)
            }
        }
        
        return scene_templates.get(scene_description, scene_templates['晴天'])
    
    def _calculate_scene_match(self, current_features: Dict, expected_features: Dict) -> float:
        """计算场景匹配度"""
        match_scores = []
        
        for feature_name, expected_range in expected_features.items():
            if feature_name in current_features:
                current_value = current_features[feature_name]
                min_val, max_val = expected_range
                
                # 计算特征值是否在期望范围内
                if min_val <= current_value <= max_val:
                    score = 1.0
                else:
                    # 计算偏离程度
                    if current_value < min_val:
                        score = max(0, 1 - (min_val - current_value) / min_val)
                    else:
                        score = max(0, 1 - (current_value - max_val) / max_val)
                
                match_scores.append(score)
        
        return np.mean(match_scores) if match_scores else 0.0
    
    def extract_geometric_features(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> Dict:
        """
        提取目标个体锚框的几何特征
        
        Args:
            image: 输入图像
            bbox: 边界框 (x, y, width, height)
            
        Returns:
            几何特征字典
        """
        x, y, w, h = bbox
        
        # 提取目标区域
        target_region = image[y:y+h, x:x+w]
        
        # 转换为灰度图像
        if len(target_region.shape) == 3:
            gray_target = cv2.cvtColor(target_region, cv2.COLOR_BGR2GRAY)
        else:
            gray_target = target_region.copy()
        
        # 计算几何特征
        features = {
            'aspect_ratio': w / h,  # 长宽比
            'circularity': self._calculate_circularity(gray_target),  # 圆度
            'hot_region_position': self._find_hot_region_position(gray_target),  # 高热区域位置
            'background_contrast': self._calculate_background_contrast(image, bbox)  # 与背景的灰度差异
        }
        
        return features
    
    def _calculate_circularity(self, image: np.ndarray) -> float:
        """计算圆度"""
        # 二值化
        _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return 0.0
        
        # 选择最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(largest_contour)
        perimeter = cv2.arcLength(largest_contour, True)
        
        if perimeter == 0:
            return 0.0
        
        # 圆度 = 4π * 面积 / 周长²
        circularity = 4 * np.pi * area / (perimeter ** 2)
        return min(circularity, 1.0)  # 限制在0-1之间
    
    def _find_hot_region_position(self, image: np.ndarray) -> Tuple[float, float]:
        """找到高热区域（最亮区域）的相对位置"""
        # 找到最大值位置
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(image)
        
        h, w = image.shape
        # 返回相对位置（0-1范围）
        rel_x = max_loc[0] / w
        rel_y = max_loc[1] / h
        
        return (rel_x, rel_y)
    
    def _calculate_background_contrast(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> float:
        """计算目标与背景的灰度差异"""
        x, y, w, h = bbox
        
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 目标区域
        target_region = gray[y:y+h, x:x+w]
        target_mean = np.mean(target_region)
        
        # 背景区域（整个图像减去目标区域）
        background_mask = np.ones_like(gray, dtype=bool)
        background_mask[y:y+h, x:x+w] = False
        background_mean = np.mean(gray[background_mask])
        
        # 计算差异
        contrast = abs(target_mean - background_mean)
        return contrast

    def query_component_relations(self, component_name: str, entity_type: str) -> Dict:
        """
        查询部件关系

        Args:
            component_name: 部件名称
            entity_type: 个体类型

        Returns:
            查询结果字典
        """
        if self.component_relations.empty:
            return {'belongs_to_entity': False, 'related_components': []}

        # 检查部件是否属于该个体类别
        belongs_query = self.component_relations[
            (self.component_relations['Head Entity'] == entity_type) &
            (self.component_relations['Tail Entity'] == component_name)
        ]

        belongs_to_entity = not belongs_query.empty

        # 查询相关部件的位置关系
        related_components = []
        if belongs_to_entity:
            # 查找与该部件相关的其他部件
            related_query = self.component_relations[
                (self.component_relations['Head Entity'] == entity_type) |
                (self.component_relations['Tail Entity'] == entity_type)
            ]

            for _, row in related_query.iterrows():
                if row['Tail Entity'] != component_name:  # 排除自身
                    relation_info = {
                        'component': row['Tail Entity'],
                        'relation': row['Relation'],
                        'position': self._determine_relative_position(row['Relation'])
                    }
                    related_components.append(relation_info)

        return {
            'belongs_to_entity': belongs_to_entity,
            'entity_type': entity_type,
            'component_name': component_name,
            'related_components': related_components
        }

    def _determine_relative_position(self, relation: str) -> str:
        """根据关系确定相对位置"""
        position_mapping = {
            '居中': 'center',
            '包含': 'inside',
            '适用': 'related',
            '前': 'front',
            '后': 'back',
            '左': 'left',
            '右': 'right',
            '左前': 'front_left',
            '右前': 'front_right',
            '左后': 'back_left',
            '右后': 'back_right'
        }
        return position_mapping.get(relation, 'unknown')

    def generate_related_bboxes(self, base_bbox: Tuple[int, int, int, int],
                               related_components: List[Dict]) -> List[Dict]:
        """
        根据相对位置关系生成相关部件的边界框

        Args:
            base_bbox: 基准部件的边界框
            related_components: 相关部件列表

        Returns:
            生成的边界框列表
        """
        x, y, w, h = base_bbox
        generated_bboxes = []

        for component in related_components:
            position = component['position']
            component_name = component['component']

            # 根据位置关系计算新的边界框
            new_bbox = self._calculate_relative_bbox(base_bbox, position)

            bbox_info = {
                'component': component_name,
                'bbox': new_bbox,
                'position': position,
                'relation': component['relation']
            }
            generated_bboxes.append(bbox_info)

        return generated_bboxes

    def _calculate_relative_bbox(self, base_bbox: Tuple[int, int, int, int],
                                position: str) -> Tuple[int, int, int, int]:
        """根据相对位置计算边界框"""
        x, y, w, h = base_bbox

        # 定义相对位置的偏移量（可根据实际需求调整）
        offset_ratio = 0.5  # 偏移比例

        position_offsets = {
            'center': (0, 0),
            'front': (0, -h * offset_ratio),
            'back': (0, h * offset_ratio),
            'left': (-w * offset_ratio, 0),
            'right': (w * offset_ratio, 0),
            'front_left': (-w * offset_ratio, -h * offset_ratio),
            'front_right': (w * offset_ratio, -h * offset_ratio),
            'back_left': (-w * offset_ratio, h * offset_ratio),
            'back_right': (w * offset_ratio, h * offset_ratio),
            'inside': (w * 0.2, h * 0.2),  # 内部位置
            'related': (w * 0.3, h * 0.3),  # 相关位置
            'unknown': (0, 0)
        }

        dx, dy = position_offsets.get(position, (0, 0))

        # 计算新的边界框（保持相同大小）
        new_x = max(0, int(x + dx))
        new_y = max(0, int(y + dy))
        new_w = int(w * 0.8)  # 稍微缩小
        new_h = int(h * 0.8)

        return (new_x, new_y, new_w, new_h)

    def visualize_results(self, image: np.ndarray,
                         individual_bbox: Tuple[int, int, int, int],
                         component_bbox: Tuple[int, int, int, int],
                         related_bboxes: List[Dict],
                         scene_info: Dict,
                         geometric_features: Dict) -> np.ndarray:
        """
        可视化分析结果

        Args:
            image: 原始图像
            individual_bbox: 个体边界框
            component_bbox: 部件边界框
            related_bboxes: 相关部件边界框列表
            scene_info: 场景信息
            geometric_features: 几何特征

        Returns:
            可视化结果图像
        """
        # 创建图像副本
        result_image = image.copy()

        # 设置颜色
        colors = {
            'individual': (0, 255, 0),    # 绿色 - 个体
            'component': (255, 0, 0),     # 红色 - 部件
            'related': (0, 0, 255)        # 蓝色 - 相关部件
        }

        # 绘制个体边界框
        x, y, w, h = individual_bbox
        cv2.rectangle(result_image, (x, y), (x + w, y + h), colors['individual'], 2)
        cv2.putText(result_image, 'Individual', (x, y - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, colors['individual'], 2)

        # 绘制部件边界框
        x, y, w, h = component_bbox
        cv2.rectangle(result_image, (x, y), (x + w, y + h), colors['component'], 2)
        cv2.putText(result_image, 'Component', (x, y - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, colors['component'], 2)

        # 绘制相关部件边界框
        for i, bbox_info in enumerate(related_bboxes):
            x, y, w, h = bbox_info['bbox']
            component_name = bbox_info['component']

            cv2.rectangle(result_image, (x, y), (x + w, y + h), colors['related'], 2)
            cv2.putText(result_image, f'{component_name}', (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, colors['related'], 1)

        # 添加信息文本
        info_text = [
            f"Scene: {scene_info.get('scene_description', 'Unknown')}",
            f"Match: {scene_info.get('match_score', 0):.2f}",
            f"Aspect Ratio: {geometric_features.get('aspect_ratio', 0):.2f}",
            f"Circularity: {geometric_features.get('circularity', 0):.2f}",
            f"Contrast: {geometric_features.get('background_contrast', 0):.1f}"
        ]

        # 在图像上添加信息
        for i, text in enumerate(info_text):
            cv2.putText(result_image, text, (10, 30 + i * 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return result_image

    def process_image(self, image_path: str, scene_description: str,
                     individual_bbox: Tuple[int, int, int, int],
                     component_bbox: Tuple[int, int, int, int],
                     entity_type: str = "电科一号",
                     component_name: str = "雷达") -> Dict:
        """
        完整的图像处理流程

        Args:
            image_path: 图像路径
            scene_description: 场景描述
            individual_bbox: 个体边界框
            component_bbox: 部件边界框
            entity_type: 个体类型
            component_name: 部件名称

        Returns:
            处理结果字典
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        # 1. 特征提取和场景对比
        features = self.extract_image_features(image)
        normalized_features = self.normalize_features(features)
        scene_comparison = self.compare_with_scene(features, scene_description)

        # 2. 几何特征提取
        geometric_features = self.extract_geometric_features(image, individual_bbox)

        # 3. 部件关系查询
        component_relations = self.query_component_relations(component_name, entity_type)

        # 4. 生成相关部件边界框
        related_bboxes = []
        if component_relations['belongs_to_entity']:
            related_bboxes = self.generate_related_bboxes(
                component_bbox, component_relations['related_components']
            )

        # 5. 可视化结果
        result_image = self.visualize_results(
            image, individual_bbox, component_bbox, related_bboxes,
            scene_comparison, geometric_features
        )

        # 整合结果
        results = {
            'image_features': features,
            'normalized_features': normalized_features.tolist(),
            'scene_comparison': scene_comparison,
            'geometric_features': geometric_features,
            'component_relations': component_relations,
            'related_bboxes': related_bboxes,
            'result_image': result_image
        }

        return results
