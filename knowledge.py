from typing import List, Dict, Tuple

BBox = Tuple[float, float, float, float]   # (x1, y1, x2, y2)
Detection = Dict[str, object]              # {"label": "A", "bbox": BBox}

# ──────────── 空间关系辅助 ────────────
def center_x(box: BBox) -> float:
    x1, _, x2, _ = box
    return (x1 + x2) / 2.0

def left_of(box_left: BBox, box_right: BBox, tol: float = 0.0) -> bool:
    """
    判断 box_left 是否在 box_right 的左侧（留一个可选容差 tol，像素）
    """
    return center_x(box_left) + tol < center_x(box_right)

def between(box_mid: BBox, box_left: BBox, box_right: BBox) -> bool:
    """
    判断 box_mid 的 x 是否位于 box_left 与 box_right 之间（不要求左右先后）
    """
    cx_mid = center_x(box_mid)
    l, r = sorted([center_x(box_left), center_x(box_right)])
    return l < cx_mid < r

# ──────────── Rule‑based 推理 ────────────
def classify_individual(detections: List[Detection]) -> str:
    """
    输入：目标检测结果 list
    输出："Individual‑1" | "Individual‑2" | "Unknown"
    """
    # 把各类别的检测框索引到 dict，若同类多框可按置信度自行挑选
    parts = {d["label"]: d["bbox"] for d in detections}

    # ─ 规则 1：A leftOf B → Individual‑1
    if {"A", "B"} <= parts.keys() and left_of(parts["A"], parts["B"]):
        return "Individual‑1"

    # ─ 规则 2：B, C, D 且 C between B D → Individual‑2
    if {"B", "C", "D"} <= parts.keys() and between(parts["C"], parts["B"], parts["D"]):
        return "Individual‑2"

    # 其余情况
    return "Unknown"

# ──────────── 示例 ────────────
if __name__ == "__main__":
    # 示例 1：符合 Individual‑1
    dets1 = [
        {"label": "A", "bbox": ( 50, 30, 100, 120)},   # 左
        {"label": "B", "bbox": (150, 40, 210, 130)},   # 右
    ]
    print(classify_individual(dets1))  # ➜ Individual‑1

    # 示例 2：符合 Individual‑2
    dets2 = [
        {"label": "B", "bbox": ( 40, 50, 100, 130)},   # 左
        {"label": "C", "bbox": (120, 60, 170, 140)},   # 中
        {"label": "D", "bbox": (200, 55, 260, 135)},   # 右
    ]
    print(classify_individual(dets2))  # ➜ Individual‑2

    # 示例 3：存疑
    dets3 = [
        {"label": "A", "bbox": (140, 40, 200, 120)},   # 右
        {"label": "B", "bbox": ( 40, 50, 100, 130)},   # 左
    ]
    print(classify_individual(dets3))  # ➜ Unknown
