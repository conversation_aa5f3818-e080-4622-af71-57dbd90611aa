import cv2
import numpy as np
from skimage.feature import graycomatrix, graycoprops
from skimage.measure import shannon_entropy
import matplotlib.pyplot as plt
def calculate_features(image):
    # Convert image to grayscale if it's not already
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Edge detection using Canny
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.mean(edges) / 255.0

    # Calculate GLCM (Gray-Level Co-occurrence Matrix)
    glcm = graycomatrix(gray, distances=[1], angles=[0], levels=256,
                         symmetric=True, normed=True)
    contrast = graycoprops(glcm, 'contrast')[0, 0]
    energy = graycoprops(glcm, 'energy')[0, 0]

    # Shannon entropy
    ent = shannon_entropy(gray.ravel())

    # Mean and standard deviation of pixel intensities
    mean_intensity = np.mean(gray)
    std_intensity = np.std(gray)

    return {
        'edge_density': edge_density,
        'contrast': contrast,
        'energy': energy,
        'entropy': ent,
        'mean_intensity': mean_intensity,
        'std_intensity': std_intensity
    }

def calculate_weights(features_visible, features_infrared):
    # Simple weighting based on feature values
    weights_visible = {}
    weights_infrared = {}

    for feature in features_visible:
        visible_value = features_visible[feature]
        infrared_value = features_infrared[feature]

        total_value = visible_value + infrared_value
        if total_value == 0:
            weights_visible[feature] = 0.5
            weights_infrared[feature] = 0.5
        else:
            weights_visible[feature] = visible_value / total_value
            weights_infrared[feature] = infrared_value / total_value

    return weights_visible, weights_infrared

def calculate_total_weight(weights_visible, weights_infrared, feature_weights):
    total_weight_visible = sum(weights_visible[feature] * feature_weights[feature] for feature in feature_weights)
    total_weight_infrared = sum(weights_infrared[feature] * feature_weights[feature] for feature in feature_weights)
    
    total_sum = total_weight_visible + total_weight_infrared
    if total_sum == 0:
        return 0.5, 0.5
    
    return total_weight_visible / total_sum, total_weight_infrared / total_sum

def determine_recognition_strategy(total_weight_visible, total_weight_infrared, threshold=0.3):
    weight_difference = abs(total_weight_visible - total_weight_infrared)
    if weight_difference > threshold:
        if total_weight_visible > total_weight_infrared:
            return "Visible Model"
        else:
            return "Infrared Model"
    else:
        return "Fusion Model"

def align_images(visible_image, infrared_image):
    # Convert images to grayscale
    visible_gray = cv2.cvtColor(visible_image, cv2.COLOR_BGR2GRAY)
    infrared_gray = cv2.cvtColor(infrared_image, cv2.COLOR_BGR2GRAY)

    # Detect ORB keypoints and descriptors
    orb = cv2.ORB_create()
    kp1, des1 = orb.detectAndCompute(visible_gray, None)
    kp2, des2 = orb.detectAndCompute(infrared_gray, None)

    # Match descriptors using BFMatcher
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    matches = bf.match(des1, des2)
    matches = sorted(matches, key=lambda x: x.distance)

    # Extract location of good matches
    points1 = np.zeros((len(matches), 2), dtype=np.float32)
    points2 = np.zeros((len(matches), 2), dtype=np.float32)

    for i, match in enumerate(matches):
        points1[i, :] = kp1[match.queryIdx].pt
        points2[i, :] = kp2[match.trainIdx].pt

    # Find homography matrix
    H, mask = cv2.findHomography(points2, points1, cv2.RANSAC)

    # Warp infrared image to align with visible image
    height, width, channels = visible_image.shape
    aligned_infrared_image = cv2.warpPerspective(infrared_image, H, (width, height))

    return aligned_infrared_image

def weighted_fusion(visible_image, infrared_image, weight_visible, weight_infrared):
    # Ensure the images are of the same size
    assert visible_image.shape == infrared_image.shape, "Images must have the same dimensions"

    # Perform weighted fusion
    fused_image = (weight_visible * visible_image.astype(float) +
                   weight_infrared * infrared_image.astype(float)).astype(np.uint8)

    return fused_image

def process_images(visible_path, infrared_path):
    visible_image = cv2.imread(visible_path)
    infrared_image = cv2.imread(infrared_path)

    if visible_image is None or infrared_image is None:
        raise ValueError("Images could not be read")

    features_visible = calculate_features(visible_image)
    features_infrared = calculate_features(infrared_image)

    weights_visible, weights_infrared = calculate_weights(features_visible, features_infrared)

    # Define weights for each feature
    feature_weights = {
        'edge_density': 0.2,
        'contrast': 0.2,
        'energy': 0.2,
        'entropy': 0.2,
        'mean_intensity': 0.1,
        'std_intensity': 0.1
    }

    total_weight_visible, total_weight_infrared = calculate_total_weight(weights_visible, weights_infrared, feature_weights)

    strategy = determine_recognition_strategy(total_weight_visible, total_weight_infrared)

    aligned_infrared_image = align_images(visible_image, infrared_image)

    if strategy == "Visible Model":
        final_image = visible_image
    elif strategy == "Infrared Model":
        final_image = aligned_infrared_image
    else:  # Fusion Model
        final_image = weighted_fusion(visible_image, aligned_infrared_image, total_weight_visible, total_weight_infrared)

    print("Visible Image Features:", features_visible)
    print("Infrared Image Features:", features_infrared)
    print("Visible Weights:", weights_visible)
    print("Infrared Weights:", weights_infrared)
    print("Total Weight Visible:", total_weight_visible)
    print("Total Weight Infrared:", total_weight_infrared)
    print("Recognition Strategy:", strategy)

    # Display the final image
    # cv2.imshow('Final Image', final_image)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()
    plt.imshow(cv2.cvtColor(final_image, cv2.COLOR_BGR2RGB))
    plt.axis('off')
    plt.show()
    
if __name__ == "__main__":
    visible_image_path = "visible/000000.jpg"
    infrared_image_path = "infrared/000000.jpg"

    process_images(visible_image_path, infrared_image_path)



