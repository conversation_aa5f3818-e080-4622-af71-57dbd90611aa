import cv2
import numpy as np
import matplotlib.pyplot as plt
def register_images(img1, img2):
    """使用 ORB 特征进行图像配准，返回img2配准到img1后的结果"""
    orb = cv2.ORB_create(5000)
    kp1, des1 = orb.detectAndCompute(img1, None)
    kp2, des2 = orb.detectAndCompute(img2, None)

    # 使用BF匹配器
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    matches = bf.match(des1, des2)

    # 按照距离排序
    matches = sorted(matches, key=lambda x: x.distance)
    pts1 = np.float32([kp1[m.queryIdx].pt for m in matches[:50]]).reshape(-1, 1, 2)
    pts2 = np.float32([kp2[m.trainIdx].pt for m in matches[:50]]).reshape(-1, 1, 2)

    # 计算变换矩阵
    H, _ = cv2.findHomography(pts2, pts1, cv2.RANSAC)

    # 应用变换矩阵将img2变换到img1坐标系
    height, width = img1.shape[:2]
    aligned_img2 = cv2.warpPerspective(img2, H, (width, height))

    return aligned_img2

def weighted_fusion(vis_img, ir_img, vis_weight=0.5, ir_weight=0.5):
    """图像加权融合"""
    vis_img = vis_img.astype(np.float32)
    ir_img = ir_img.astype(np.float32)
    
    fused = vis_weight * vis_img + ir_weight * ir_img
    fused = np.clip(fused, 0, 255).astype(np.uint8)
    return fused

def fusion_pipeline(vis_img, ir_img, vis_weight=0.5, ir_weight=0.5):
    """整合配准和融合流程"""
    # 将图像转换为灰度进行配准
    vis_gray = cv2.cvtColor(vis_img, cv2.COLOR_BGR2GRAY)
    ir_gray = ir_img if len(ir_img.shape) == 2 else cv2.cvtColor(ir_img, cv2.COLOR_BGR2GRAY)

    # 将红外图像配准到可见光图像
    aligned_ir = register_images(vis_gray, ir_gray)

    # 转为伪彩色对齐（红外可以映射为红通道）
    vis_rgb = cv2.cvtColor(vis_gray, cv2.COLOR_GRAY2BGR)
    ir_rgb = cv2.cvtColor(aligned_ir, cv2.COLOR_GRAY2BGR)

    # 进行加权融合
    fused_img = weighted_fusion(vis_rgb, ir_rgb, vis_weight, ir_weight)
    return fused_img

if __name__ == "__main__":
    vis_img = cv2.imread("visible/V_000000.jpg")  # 可见光图像
    ir_img = cv2.imread("infrared/T_000000.jpg", 0)  # 红外图像（一般是单通道）

    # 假设你已有识别权重（0~1之间）
    vis_weight = 0.6
    ir_weight = 0.4

    fused_img = fusion_pipeline(vis_img, ir_img, vis_weight, ir_weight)

    # cv2.imshow("Fused Image", fused_img)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()
    if vis_img is None or ir_img is None:
        raise ValueError("Images could not be read")
    plt.figure(figsize=(12, 6))
    plt.subplot(1, 3, 1)
    plt.title("Visible Image")
    plt.imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))    
    plt.axis('off')
    plt.subplot(1, 3, 2)
    plt.title("Infrared Image")
    plt.imshow(ir_img, cmap='gray')
    plt.axis('off')
    plt.subplot(1, 3, 3)
    plt.title("Fused Image")
    plt.imshow(cv2.cvtColor(fused_img, cv2.COLOR_BGR2RGB))
    plt.axis('off')
    plt.tight_layout()  
    plt.show()
