"""
图像分析系统配置文件
"""

# 文件路径配置
PATHS = {
    'csv_file': 'benti/diankeyihao.csv',
    'output_dir': 'output',
    'demo_images_dir': 'demo_images'
}

# 场景识别配置
SCENE_TEMPLATES = {
    '晴天': {
        'mean_intensity': (120, 200),
        'contrast': (20, 50),
        'entropy': (4.0, 6.0),
        'edge_density': (0.01, 0.05),
        'structure_complexity': (50, 150)
    },
    '雨天': {
        'mean_intensity': (80, 150),
        'contrast': (15, 40),
        'entropy': (3.5, 5.5),
        'edge_density': (0.005, 0.03),
        'structure_complexity': (30, 100)
    },
    '雾天': {
        'mean_intensity': (100, 180),
        'contrast': (10, 30),
        'entropy': (3.0, 5.0),
        'edge_density': (0.003, 0.02),
        'structure_complexity': (20, 80)
    },
    '夜晚': {
        'mean_intensity': (30, 100),
        'contrast': (25, 60),
        'entropy': (4.5, 7.0),
        'edge_density': (0.01, 0.04),
        'structure_complexity': (40, 120)
    }
}

# 几何特征配置
GEOMETRIC_CONFIG = {
    'circularity_threshold': 0.7,  # 圆度阈值
    'aspect_ratio_range': (0.5, 3.0),  # 长宽比范围
    'contrast_threshold': 20,  # 对比度阈值
    'hot_region_threshold': 0.8  # 高热区域阈值
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'colors': {
        'individual': (0, 255, 0),    # 绿色 - 个体
        'component': (255, 0, 0),     # 红色 - 部件
        'related': (0, 0, 255),       # 蓝色 - 相关部件
        'text': (255, 255, 255)       # 白色 - 文本
    },
    'line_thickness': 2,
    'font_scale': 0.7,
    'font_thickness': 2
}

# 相对位置配置
POSITION_OFFSETS = {
    'center': (0, 0),
    'front': (0, -0.5),
    'back': (0, 0.5),
    'left': (-0.5, 0),
    'right': (0.5, 0),
    'front_left': (-0.5, -0.5),
    'front_right': (0.5, -0.5),
    'back_left': (-0.5, 0.5),
    'back_right': (0.5, 0.5),
    'inside': (0.2, 0.2),
    'related': (0.3, 0.3),
    'unknown': (0, 0)
}

# 系统参数
SYSTEM_CONFIG = {
    'scene_match_threshold': 0.7,  # 场景匹配阈值
    'feature_normalization': True,  # 是否进行特征归一化
    'debug_mode': True,  # 调试模式
    'save_intermediate_results': True,  # 保存中间结果
    'image_resize_max': 1024  # 图像最大尺寸
}

# 默认参数
DEFAULT_PARAMS = {
    'entity_type': '电科一号',
    'component_name': '雷达',
    'scene_description': '晴天',
    'individual_bbox': (100, 100, 200, 150),
    'component_bbox': (150, 125, 80, 60)
}

# 支持的图像格式
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']

# 输出配置
OUTPUT_CONFIG = {
    'save_result_image': True,
    'save_analysis_report': True,
    'save_feature_data': True,
    'result_image_quality': 95,  # JPEG质量
    'report_format': 'json'  # 报告格式：json, txt, csv
}
