import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import os
import json
from typing import List, Dict, Tuple, Optional
#显示中文
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文

class SeaSceneAnalyzer:
    def __init__(self, scene_types: List[str], object_types: List[str], component_types: List[str]):
        """初始化场景分析器
        
        Args:
            scene_types: 场景类型列表
            object_types: 目标个体类型列表
            component_types: 部件类型列表
        """
        self.scene_types = scene_types
        self.object_types = object_types
        self.component_types = component_types
        self.scaler = MinMaxScaler()
        self.component_db = self._load_component_database()
        
    def _load_component_database(self) -> Dict[str, pd.DataFrame]:
        """加载部件数据库"""
        db = {}
        for obj_type in self.object_types:
            file_path = f"{obj_type}_components.csv"
            if os.path.exists(file_path):
                db[obj_type] = pd.read_csv(file_path)
        return db
        
    def analyze(self, image_path: str, scene_type: str, 
                object_boxes: List[Dict], component_boxes: List[Dict]) -> Dict:
        """分析图像
        
        Args:
            image_path: 图像路径
            scene_type: 场景类型
            object_boxes: 目标个体锚框列表
            component_boxes: 部件锚框列表
            
        Returns:
            分析结果字典
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise FileNotFoundError(f"无法读取图像: {image_path}")
            
        # 转换为RGB格式用于显示
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 步骤1: 场景特征提取与验证
        scene_features = self._extract_scene_features(image)
        scene_normalized = self._normalize_features(scene_features)
        scene_match = self._validate_scene(scene_normalized, scene_type)
        
        # 步骤2: 目标个体特征提取
        object_features = []
        for obj_box in object_boxes:
            obj_roi = self._extract_roi(image, obj_box['bbox'])
            features = self._extract_object_features(obj_roi, obj_box['bbox'])
            features['type'] = obj_box['type']
            object_features.append(features)
        
        # 步骤3: 部件分析
        component_analysis = []
        for comp_box in component_boxes:
            analysis = self._analyze_component(comp_box, object_boxes)
            component_analysis.append(analysis)
            
        # 步骤4: 可视化结果
        result_image = self._visualize_results(
            image_rgb.copy(), object_boxes, component_boxes, component_analysis)
            
        # 构建完整的分析结果
        analysis_results = {
            'image_path': image_path,
            'scene_input': scene_type,
            'scene_analysis': {
                'features': scene_features,
                'normalized_features': scene_normalized,
                'scene_match': scene_match,
                'match_criteria': self._get_scene_match_criteria(scene_type)
            },
            'objects': [{
                'type': obj['type'],
                'bounding_box': obj_box['bbox'],
                'features': obj,
                'components': [comp for comp in component_analysis 
                              if comp['parent_object'] == obj['type']]
            } for obj, obj_box in zip(object_features, object_boxes)],
            'unassigned_components': [comp for comp in component_analysis 
                                     if not comp['is_valid']]
        }
            
        return {
            'analysis_results': analysis_results,
            'result_image': result_image
        }
    
    def _extract_scene_features(self, image: np.ndarray) -> Dict[str, float]:
        """提取场景特征
        
        Args:
            image: 输入图像
            
        Returns:
            特征字典
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 计算亮度均值和标准差
        brightness_mean = np.mean(gray)
        brightness_std = np.std(gray)
        
        # 计算颜色直方图特征
        hist_b = cv2.calcHist([image], [0], None, [8], [0, 256]).flatten()
        hist_g = cv2.calcHist([image], [1], None, [8], [0, 256]).flatten()
        hist_r = cv2.calcHist([image], [2], None, [8], [0, 256]).flatten()
        color_features = np.concatenate([hist_b, hist_g, hist_r])
        
        # 计算边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges) / (edges.shape[0] * edges.shape[1])
        
        return {
            'brightness_mean': brightness_mean,
            'brightness_std': brightness_std,
            'edge_density': edge_density,
            **{f'color_{i}': val for i, val in enumerate(color_features)}
        }
    
    def _normalize_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """归一化特征
        
        Args:
            features: 特征字典
            
        Returns:
            归一化后的特征字典
        """
        # 提取特征值
        feature_values = np.array(list(features.values())).reshape(1, -1)
        
        # 归一化处理
        normalized_values = self.scaler.fit_transform(feature_values).flatten()
        
        # 构建归一化后的特征字典
        return {key: val for key, val in zip(features.keys(), normalized_values)}
    
    def _validate_scene(self, features: Dict[str, float], scene_type: str) -> bool:
        """验证场景类型
        
        Args:
            features: 特征字典
            scene_type: 场景类型
            
        Returns:
            是否匹配
        """
        # 基于特征判断场景类型的简单实现
        if scene_type == '白天':
            return features['brightness_mean'] > 0.5 and features['edge_density'] > 0.1
        elif scene_type == '晚上':
            return features['brightness_mean'] < 0.3 and features['brightness_std'] > 0.2
        elif scene_type == '雨天':
            return features['brightness_std'] < 0.15 and features['color_0'] > 0.3
        elif scene_type == '雾天':
            return features['brightness_std'] < 0.1 and features['edge_density'] < 0.05
        return False
    
    def _get_scene_match_criteria(self, scene_type: str) -> Dict:
        """获取场景匹配的判断标准
        
        Args:
            scene_type: 场景类型
            
        Returns:
            判断标准字典
        """
        if scene_type == '白天':
            return {
                'brightness_mean': '> 0.5',
                'edge_density': '> 0.1'
            }
        elif scene_type == '晚上':
            return {
                'brightness_mean': '< 0.3',
                'brightness_std': '> 0.2'
            }
        elif scene_type == '雨天':
            return {
                'brightness_std': '< 0.15',
                'color_0': '> 0.3'
            }
        elif scene_type == '雾天':
            return {
                'brightness_std': '< 0.1',
                'edge_density': '< 0.05'
            }
        return {}
    
    def _extract_roi(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """提取感兴趣区域
        
        Args:
            image: 输入图像
            bbox: 边界框 (x, y, w, h)
            
        Returns:
            ROI图像
        """
        x, y, w, h = bbox
        return image[y:y+h, x:x+w]
    
    def _extract_object_features(self, roi: np.ndarray, bbox: Tuple[int, int, int, int]) -> Dict[str, float]:
        """提取目标个体特征
        
        Args:
            roi: 目标区域图像
            bbox: 边界框 (x, y, w, h)
            
        Returns:
            特征字典
        """
        x, y, w, h = bbox
        
        # 计算长宽比
        aspect_ratio = w / h if h > 0 else 0
        
        # 计算圆度
        area = w * h
        perimeter = 2 * (w + h)
        roundness = 4 * np.pi * area / (perimeter ** 2) if perimeter > 0 else 0
        
        # 转换为HSV颜色空间检测高热区域（红色）
        hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
        lower_red1 = np.array([0, 100, 100])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 100, 100])
        upper_red2 = np.array([180, 255, 255])
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = mask1 + mask2
        
        # 计算高热区域的质心
        M = cv2.moments(red_mask)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            # 计算高热区域在目标矩形中的相对位置
            heat_position_x = cx / w
            heat_position_y = cy / h
        else:
            heat_position_x, heat_position_y = -1, -1
        
        # 计算与背景的灰度差异
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        roi_mean = np.mean(gray_roi)
        
        # 计算与背景的灰度差异（需要先提取背景）
        # 这里简化处理，假设背景是整个图像的平均灰度
        full_gray = cv2.cvtColor(cv2.imread('000003.jpg'), cv2.COLOR_BGR2GRAY)
        background_mean = np.mean(full_gray)
        gray_diff = abs(roi_mean - background_mean)
        
        return {
            'aspect_ratio': aspect_ratio,
            'roundness': roundness,
            'heat_position_x': heat_position_x,
            'heat_position_y': heat_position_y,
            'gray_value': roi_mean,
            'gray_diff_with_background': gray_diff
        }
    
    def _analyze_component(self, comp_box: Dict, object_boxes: List[Dict]) -> Dict:
        """分析部件与目标个体的关系
        
        Args:
            comp_box: 部件锚框
            object_boxes: 目标个体锚框列表
            
        Returns:
            分析结果
        """
        comp_type = comp_box['type']
        comp_bbox = comp_box['bbox']
        comp_x, comp_y, comp_w, comp_h = comp_bbox
        
        # 查找包含此部件的最近目标个体
        best_match = None
        min_distance = float('inf')
        
        for obj_box in object_boxes:
            obj_type = obj_box['type']
            obj_bbox = obj_box['bbox']
            obj_x, obj_y, obj_w, obj_h = obj_bbox
            
            # 检查部件是否在目标个体内部
            if (comp_x >= obj_x and comp_y >= obj_y and 
                comp_x + comp_w <= obj_x + obj_w and 
                comp_y + comp_h <= obj_y + obj_h):
                
                # 计算部件中心到个体中心的距离
                comp_center = (comp_x + comp_w/2, comp_y + comp_h/2)
                obj_center = (obj_x + obj_w/2, obj_y + obj_h/2)
                distance = np.sqrt((comp_center[0] - obj_center[0])**2 + 
                                   (comp_center[1] - obj_center[1])**2)
                
                if distance < min_distance:
                    min_distance = distance
                    best_match = obj_box
        
        analysis = {
            'component_type': comp_type,
            'component_bbox': comp_bbox,
            'is_valid': best_match is not None,
            'parent_object': best_match['type'] if best_match else None,
            'relative_positions': {}
        }
        
        if best_match:
            # 查询数据库获取该个体的其他部件位置信息
            obj_type = best_match['type']
            if obj_type in self.component_db:
                # 获取当前部件的信息
                comp_data = self.component_db[obj_type]
                current_comp_info = comp_data[comp_data['component'] == comp_type].iloc[0]
                
                # 获取其他部件的相对位置
                for _, row in comp_data.iterrows():
                    if row['component'] != comp_type:
                        # 计算相对位置
                        rel_position = self._calculate_relative_position(
                            current_comp_info, row)
                        analysis['relative_positions'][row['component']] = rel_position
        
        return analysis
    
    def _calculate_relative_position(self, comp1: pd.Series, comp2: pd.Series) -> str:
        """计算两个部件的相对位置
        
        Args:
            comp1: 第一个部件信息
            comp2: 第二个部件信息
            
        Returns:
            相对位置描述
        """
        # 获取部件的相对位置信息
        x1, y1 = comp1['x'], comp1['y']
        x2, y2 = comp2['x'], comp2['y']
        
        # 计算相对位置
        dx = x2 - x1
        dy = y2 - y1
        
        # 判断方向
        if dx > 0 and abs(dy/dx) < 0.5:
            return '右'
        elif dx < 0 and abs(dy/dx) < 0.5:
            return '左'
        elif dy > 0 and abs(dx/dy) < 0.5:
            return '后'
        elif dy < 0 and abs(dx/dy) < 0.5:
            return '前'
        elif dx > 0 and dy > 0:
            return '右后'
        elif dx > 0 and dy < 0:
            return '右前'
        elif dx < 0 and dy > 0:
            return '左后'
        else:
            return '左前'
    
    def _visualize_results(self, image: np.ndarray, object_boxes: List[Dict], 
                          component_boxes: List[Dict], component_analysis: List[Dict]) -> np.ndarray:
        """可视化分析结果
        
        Args:
            image: 输入图像
            object_boxes: 目标个体锚框列表
            component_boxes: 部件锚框列表
            component_analysis: 部件分析结果列表
            
        Returns:
            可视化结果图像
        """
        # 为不同类型的目标和部件定义颜色
        object_colors = {
            '油船': (0, 255, 0),    # 绿色
            '货船': (0, 0, 255)     # 红色
        }
        
        component_colors = {
            '烟囱': (255, 0, 0),    # 蓝色
            '指挥室': (255, 255, 0), # 青色
            '雷达': (255, 0, 255)   # 紫色
        }
        
        # 绘制目标个体边界框
        for obj_box in object_boxes:
            obj_type = obj_box['type']
            bbox = obj_box['bbox']
            x, y, w, h = bbox
            color = object_colors.get(obj_type, (255, 255, 255))
            
            # 绘制边界框
            cv2.rectangle(image, (x, y), (x+w, y+h), color, 2)
            
            # 添加标签
            cv2.putText(image, obj_type, (x, y-10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, color, 2)
        
        # 绘制部件边界框和分析结果
        for comp_box, comp_analysis in zip(component_boxes, component_analysis):
            comp_type = comp_box['type']
            bbox = comp_box['bbox']
            x, y, w, h = bbox
            color = component_colors.get(comp_type, (255, 255, 255))
            
            # 绘制边界框
            cv2.rectangle(image, (x, y), (x+w, y+h), color, 2)
            
            # 添加标签
            label = comp_type
            if comp_analysis['is_valid']:
                label += f"({comp_analysis['parent_object']})"
            cv2.putText(image, label, (x, y-10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            # 绘制其他部件的相对位置预测
            for other_comp, position in comp_analysis['relative_positions'].items():
                # 获取父对象的边界框
                parent_obj = next((obj for obj in object_boxes 
                                  if obj['type'] == comp_analysis['parent_object']), None)
                if parent_obj:
                    obj_x, obj_y, obj_w, obj_h = parent_obj['bbox']
                    
                    # 计算相对位置的中心点
                    center_x = x + w//2
                    center_y = y + h//2
                    
                    # 根据相对位置计算偏移
                    rel_pos = position
                    offset_x, offset_y = 0, 0
                    
                    if '右' in rel_pos:
                        offset_x = obj_w // 3
                    elif '左' in rel_pos:
                        offset_x = -obj_w // 3
                        
                    if '后' in rel_pos:
                        offset_y = obj_h // 3
                    elif '前' in rel_pos:
                        offset_y = -obj_h // 3
                    
                    # 计算预测位置
                    pred_x = center_x + offset_x
                    pred_y = center_y + offset_y
                    
                    # 绘制预测位置
                    cv2.circle(image, (pred_x, pred_y), 5, (0, 255, 255), -1)
                    
                    # 添加标签
                    cv2.putText(image, f"{other_comp}({rel_pos})", 
                                (pred_x+10, pred_y), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        
        return image
    
    def save_analysis_to_json(self, results: Dict, output_path: str) -> None:
        """将分析结果保存为JSON格式
        
        Args:
            results: 分析结果字典
            output_path: 输出文件路径
        """
        # 递归转换NumPy数组、数值和布尔类型为JSON可序列化格式
        def convert_to_serializable(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            if isinstance(obj, (np.float32, np.float64)):
                return float(obj)
            if isinstance(obj, (np.int32, np.int64)):
                return int(obj)
            if isinstance(obj, (np.bool_,)):
                return bool(obj)
            if isinstance(obj, dict):
                return {k: convert_to_serializable(v) for k, v in obj.items()}
            if isinstance(obj, list):
                return [convert_to_serializable(i) for i in obj]
            return obj
            
        # 递归处理结果字典
        serializable_results = convert_to_serializable(results)
        
        # 保存为JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=4)
        
        print(f"分析结果已保存至: {output_path}")

def main():
    # 初始化场景分析器
    analyzer = SeaSceneAnalyzer(
        scene_types=['白天', '晚上', '雨天', '雾天'],
        object_types=['油船', '货船'],
        component_types=['烟囱', '指挥室', '雷达']
    )
    
    # 示例输入
    image_path = '000003.jpg'  # 请替换为实际图像路径
    
    # 场景类型
    scene_type = '白天'
    
    # 目标个体锚框 [x, y, width, height]
    object_boxes = [
        {
            'type': '油船',
            'bbox': (100, 150, 200, 100)
        },
        {
            'type': '货船',
            'bbox': (400, 200, 180, 120)
        }
    ]
    
    # 部件锚框
    component_boxes = [
        {
            'type': '烟囱',
            'bbox': (180, 150, 30, 60)
        },
        {
            'type': '指挥室',
            'bbox': (250, 160, 40, 40)
        },
        {
            'type': '雷达',
            'bbox': (450, 200, 25, 30)
        }
    ]
    
    try:
        # 执行分析
        results = analyzer.analyze(image_path, scene_type, object_boxes, component_boxes)
        
        # 保存分析结果到JSON
        analyzer.save_analysis_to_json(results['analysis_results'], 'scene_analysis.json')
        
        # 显示结果
        plt.figure(figsize=(12, 8))
        plt.imshow(results['result_image'])
        plt.title('分析结果可视化')
        plt.axis('off')
        plt.show()
        
        # 打印分析结果
        print(f"场景匹配结果: {scene_type} ({'匹配' if results['analysis_results']['scene_analysis']['scene_match'] else '不匹配'})")
        
    except Exception as e:
        print(f"分析过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()