# 可见光与红外图像/视频特征权重计算器

这是一个基于PyQt5的实时图像/视频特征分析和权重分配系统，用于计算可见光和红外探测体制的可识别权重。

## 功能特性

### 主要功能
- 🖼️ **图像模式**: 支持静态图像的特征分析
- 🎬 **视频模式**: 支持实时视频流的特征分析
- 📊 **特征计算**: 计算6种核心特征
  - 结构特征（拉普拉斯算子方差）
  - 信息熵
  - 灰度值
  - 对比度
  - 高频复杂度（基于FFT）
  - 能量特征
- ⚖️ **权重分配**: 基于特征的可见光/红外权重计算
- 📈 **实时可视化**: 柱状图和饼图展示
- 🔍 **详细分析**: 特征提取结果子窗口

### 界面特性
- 现代化的PyQt5界面设计
- 选项卡式布局（图像/视频模式）
- 可调节的特征权重滑块
- 实时FPS显示
- 进度条显示处理状态
- 详细的处理日志

## 系统要求

- Python 3.7+
- Windows/Linux/macOS
- 支持的图像格式：JPG, PNG, BMP, TIFF
- 支持的视频格式：MP4, AVI, MOV, MKV

## 安装说明

1. **克隆或下载项目文件**
2. **安装依赖包**：
   ```bash
   pip install -r requirements.txt
   ```
3. **运行应用程序**：
   ```bash
   python run_app.py
   ```

## 使用指南

### 图像模式
1. 点击"图像模式"选项卡
2. 分别选择可见光和红外图像文件
3. 调整特征权重滑块（可选）
4. 点击"处理图像"按钮
5. 查看柱状图和饼图结果
6. 点击"查看特征详情"获取详细信息

### 视频模式
1. 点击"视频模式"选项卡
2. 分别选择可见光和红外视频文件
3. 点击"播放"按钮开始实时分析
4. 观察实时更新的特征图表
5. 使用"暂停"/"停止"控制播放

### 特征权重调节
- 使用界面下方的滑块调节各特征权重
- 权重范围：0.0 - 1.0
- 实时影响权重计算结果

## 技术原理

### 特征计算算法

1. **结构特征**
   - 使用拉普拉斯算子计算图像二阶导数的方差
   - 反映图像边缘和细节丰富程度

2. **信息熵**
   - 基于像素灰度直方图计算
   - 度量图像信息量和复杂度

3. **对比度**
   - 标准差与均值的比值
   - 反映图像动态范围

4. **高频复杂度**
   - 基于FFT的高频能量分析
   - 使用高通滤波器提取高频分量

5. **能量特征**
   - 归一化像素值平方的均值
   - 反映图像整体亮度能量

### 权重分配算法
1. 特征归一化（最小-最大归一化）
2. 加权求和计算各探测体制得分
3. 归一化得到最终权重分配

## 配置文件

系统支持通过`config.json`文件进行配置：
- 预设权重配置
- 处理参数设置
- 可视化选项

### 预设权重模式
- **默认均匀权重**: 所有特征等权重
- **目标检测优化**: 突出结构和对比度
- **夜视增强**: 强调对比度和信息熵
- **细节保持**: 突出结构和高频特征

## 文件结构

```
test_project/
├── dispatch_system.py      # 主程序文件
├── run_app.py             # 启动脚本
├── requirements.txt       # 依赖包列表
├── config.json           # 配置文件
├── README.md             # 说明文档
├── visible/              # 可见光图像示例
├── infrared/             # 红外图像示例
├── V_video/              # 可见光视频示例
└── T_video/              # 红外视频示例
```

## 性能优化

- 多线程图像处理，避免UI阻塞
- 智能内存管理
- 自适应算法参数
- 错误处理和异常恢复

## 故障排除

### 常见问题

1. **导入错误**
   - 确保已安装所有依赖包
   - 检查Python版本兼容性

2. **视频播放问题**
   - 验证视频文件格式
   - 检查文件路径是否正确

3. **特征计算异常**
   - 确保图像文件完整有效
   - 检查图像分辨率和格式

4. **界面显示问题**
   - 检查系统DPI设置
   - 确保显卡驱动更新

## 开发信息

- **开发语言**: Python 3
- **UI框架**: PyQt5
- **图像处理**: OpenCV
- **科学计算**: NumPy
- **数据可视化**: Matplotlib
- **开发日期**: 2025年6月22日

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 请确保输入的可见光和红外图像/视频在时间和空间上保持同步，以获得最佳的分析效果。
