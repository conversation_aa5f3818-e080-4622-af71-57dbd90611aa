import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import filedialog, ttk
import time
from scipy import ndimage

class MultimodalImageAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("多模态图像特征提取与权重分析系统")
        self.root.geometry("1200x800")
        
        # 设置中文字体支持
        plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
        
        # 初始化变量
        self.visible_path = None
        self.thermal_path = None
        self.is_video = False
        self.stop_processing = False
        
        # 创建界面
        self.create_widgets()
        
        # 特征权重参数
        self.feature_weights = {
            '边缘特征': 0.2,
            '结构特征': 0.2,
            '信息熵': 0.2,
            '灰度特征': 0.2,
            '对比度': 0.2
        }
        
        # 场景判断阈值
        self.scene_thresholds = {
            '边缘特征': 0.02,    # 夜晚边缘密度通常较低
            '结构特征': 500,     # 夜晚结构方差通常较低
            '信息熵': 5.0,       # 夜晚信息熵通常较低
            '灰度特征': 0.15,    # 夜晚灰度特征值通常较低
            '对比度': 0.08       # 夜晚对比度通常较低
        }
        
        # 场景判断权重
        self.scene_weights = {
            '边缘特征': 0.2,
            '结构特征': 0.2,
            '信息熵': 0.2,
            '灰度特征': 0.2,
            '对比度': 0.2
        }
        
        # 初始化图形
        self.init_plots()
    
    def create_widgets(self):
        # 顶部文件选择区域
        file_frame = ttk.Frame(self.root, padding="10")
        file_frame.pack(fill=tk.X)
        
        ttk.Label(file_frame, text="可见光文件:").grid(row=0, column=0, padx=5, pady=5)
        self.visible_entry = ttk.Entry(file_frame, width=50)
        self.visible_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_visible).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Label(file_frame, text="红外文件:").grid(row=1, column=0, padx=5, pady=5)
        self.thermal_entry = ttk.Entry(file_frame, width=50)
        self.thermal_entry.grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_thermal).grid(row=1, column=2, padx=5, pady=5)
        
        # 视频/图像选择
        ttk.Label(file_frame, text="输入类型:").grid(row=0, column=3, padx=5, pady=5)
        self.input_type = tk.StringVar(value="图像")
        ttk.Radiobutton(file_frame, text="图像", variable=self.input_type, value="图像").grid(row=0, column=4, padx=5, pady=5)
        ttk.Radiobutton(file_frame, text="视频", variable=self.input_type, value="视频").grid(row=0, column=5, padx=5, pady=5)
        
        # 处理按钮
        ttk.Button(file_frame, text="开始处理", command=self.start_processing).grid(row=1, column=4, padx=5, pady=5)
        ttk.Button(file_frame, text="停止处理", command=self.stop_processing_func).grid(row=1, column=5, padx=5, pady=5)
        
        # 结果显示区域
        results_frame = ttk.Frame(self.root, padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # 原始图像显示区域
        images_frame = ttk.LabelFrame(results_frame, text="原始图像", padding="10")
        images_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.visible_label = ttk.Label(images_frame)
        self.visible_label.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        
        self.thermal_label = ttk.Label(images_frame)
        self.thermal_label.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True)
        
        # 新增场景判断结果显示
        self.scene_label = ttk.Label(images_frame, text="场景判断: 未处理", font=("SimHei", 12, "bold"))
        self.scene_label.pack(side=tk.BOTTOM, fill=tk.X, pady=10)
        
        # 特征和权重可视化区域
        viz_frame = ttk.LabelFrame(results_frame, text="特征分析与权重可视化", padding="10")
        viz_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建图形画布
        self.figure = plt.figure(figsize=(8, 10))
        self.canvas = FigureCanvasTkAgg(self.figure, master=viz_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def init_plots(self):
        self.figure.clear()
        
        # 创建特征柱状图
        self.ax1 = self.figure.add_subplot(211)
        self.ax1.set_title('特征提取结果')
        self.ax1.set_xlabel('特征类型')
        self.ax1.set_ylabel('特征值')
        
        # 创建权重饼图
        self.ax2 = self.figure.add_subplot(212)
        self.ax2.set_title('模型分配权重')
        
        self.figure.tight_layout()
        self.canvas.draw()
    
    def browse_visible(self):
        file_path = filedialog.askopenfilename(
            title="选择可见光文件",
            filetypes=[("图像文件", "*.png;*.jpg;*.jpeg"), ("视频文件", "*.mp4;*.avi;*.mov")]
        )
        if file_path:
            self.visible_path = file_path
            self.visible_entry.delete(0, tk.END)
            self.visible_entry.insert(0, file_path)
    
    def browse_thermal(self):
        file_path = filedialog.askopenfilename(
            title="选择红外文件",
            filetypes=[("图像文件", "*.png;*.jpg;*.jpeg"), ("视频文件", "*.mp4;*.avi;*.mov")]
        )
        if file_path:
            self.thermal_path = file_path
            self.thermal_entry.delete(0, tk.END)
            self.thermal_entry.insert(0, file_path)
    
    def start_processing(self):
        self.stop_processing = False
        
        if not self.visible_path or not self.thermal_path:
            tk.messagebox.showerror("错误", "请选择可见光和红外文件")
            return
        
        self.is_video = self.input_type.get() == "视频"
        
        if self.is_video:
            self.process_video()
        else:
            self.process_image()
    
    def stop_processing_func(self):
        self.stop_processing = True
    
    def process_image(self):
        try:
            # 读取图像
            visible_img = cv2.imread(self.visible_path)
            thermal_img = cv2.imread(self.thermal_path)
            
            if visible_img is None or thermal_img is None:
                tk.messagebox.showerror("错误", "无法读取图像文件")
                return
            
            # 调整图像大小以匹配
            thermal_img = cv2.resize(thermal_img, (visible_img.shape[1], visible_img.shape[0]))
            
            # 显示图像
            self.display_images(visible_img, thermal_img)
            
            # 特征提取
            visible_features = self.extract_features(visible_img)
            thermal_features = self.extract_features(thermal_img)
            
            # 计算总权重
            visible_weight, thermal_weight = self.calculate_weights(visible_features, thermal_features)
            
            # 判断场景
            scene = self.judge_scene(visible_features)
            self.scene_label.config(text=f"场景判断: {scene}")
            
            # 可视化结果
            self.visualize_results(visible_features, thermal_features, visible_weight, thermal_weight)
            
        except Exception as e:
            tk.messagebox.showerror("处理错误", f"处理图像时出错: {str(e)}")
    
    def process_video(self):
        try:
            # 打开视频文件
            visible_cap = cv2.VideoCapture(self.visible_path)
            thermal_cap = cv2.VideoCapture(self.thermal_path)
            
            if not visible_cap.isOpened() or not thermal_cap.isOpened():
                tk.messagebox.showerror("错误", "无法打开视频文件")
                return
            
            # 获取视频帧率
            fps = visible_cap.get(cv2.CAP_PROP_FPS)
            delay = int(1000 / fps)
            
            while True:
                if self.stop_processing:
                    break
                
                ret1, visible_frame = visible_cap.read()
                ret2, thermal_frame = thermal_cap.read()
                
                if not ret1 or not ret2:
                    break
                
                # 调整图像大小以匹配
                thermal_frame = cv2.resize(thermal_frame, (visible_frame.shape[1], visible_frame.shape[0]))
                
                # 显示图像
                self.display_images(visible_frame, thermal_frame)
                
                # 特征提取
                visible_features = self.extract_features(visible_frame)
                thermal_features = self.extract_features(thermal_frame)
                
                # 计算总权重
                visible_weight, thermal_weight = self.calculate_weights(visible_features, thermal_features)
                
                # 判断场景
                scene = self.judge_scene(visible_features)
                self.scene_label.config(text=f"场景判断: {scene}")
                
                # 可视化结果
                self.visualize_results(visible_features, thermal_features, visible_weight, thermal_weight)
                
                # 刷新界面
                self.root.update()
                time.sleep(0.1)  # 控制处理速度
            
            # 释放资源
            visible_cap.release()
            thermal_cap.release()
            
        except Exception as e:
            tk.messagebox.showerror("处理错误", f"处理视频时出错: {str(e)}")
    
    def display_images(self, visible_img, thermal_img):
        # 转换为RGB
        visible_rgb = cv2.cvtColor(visible_img, cv2.COLOR_BGR2RGB)
        thermal_rgb = cv2.cvtColor(thermal_img, cv2.COLOR_BGR2RGB)
        
        # 调整图像大小以适应显示区域
        max_height = 300
        visible_height, visible_width = visible_rgb.shape[:2]
        thermal_height, thermal_width = thermal_rgb.shape[:2]
        
        visible_scale = max_height / visible_height
        thermal_scale = max_height / thermal_height
        
        visible_rgb = cv2.resize(visible_rgb, (int(visible_width * visible_scale), max_height))
        thermal_rgb = cv2.resize(thermal_rgb, (int(thermal_width * thermal_scale), max_height))
        
        # 转换为Tkinter可用的格式
        visible_img_tk = self.convert_to_tk_image(visible_rgb)
        thermal_img_tk = self.convert_to_tk_image(thermal_rgb)
        
        # 更新显示
        self.visible_label.config(image=visible_img_tk)
        self.visible_label.image = visible_img_tk
        
        self.thermal_label.config(image=thermal_img_tk)
        self.thermal_label.image = thermal_img_tk
    
    def convert_to_tk_image(self, img):
        from PIL import Image, ImageTk
        pil_img = Image.fromarray(img)
        return ImageTk.PhotoImage(image=pil_img)
    
    def extract_features(self, img):
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 1. 边缘特征 - 使用Canny边缘检测
        edges = cv2.Canny(gray, 100, 200)
        edge_density = np.sum(edges) / (edges.shape[0] * edges.shape[1])
        
        # 2. 结构特征 - 使用拉普拉斯算子检测图像的二阶导数
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        structure = np.var(laplacian)
        
        # 3. 信息熵 - 计算图像的熵
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist_norm = hist.ravel() / hist.sum()
        entropy = -np.sum(hist_norm * np.log2(hist_norm + 1e-10))
        
        # 4. 灰度特征 - 计算平均灰度值和标准差
        mean_gray = np.mean(gray)
        std_gray = np.std(gray)
        gray_feature = (mean_gray / 255.0) * std_gray  # 归一化处理
        
        # 5. 对比度 - 使用CLAHE的对比度增强程度作为对比度指标
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        cl1 = clahe.apply(gray)
        contrast = np.mean(np.abs(cl1.astype(np.int32) - gray.astype(np.int32))) / 255.0
        
        return {
            '边缘特征': edge_density,
            '结构特征': structure,
            '信息熵': entropy,
            '灰度特征': gray_feature,
            '对比度': contrast
        }
    
    def calculate_weights(self, visible_features, thermal_features):
        # 初始化权重
        visible_total = 0
        thermal_total = 0
        
        # 计算每种特征的权重贡献
        for feature, weight in self.feature_weights.items():
            visible_value = visible_features[feature]
            thermal_value = thermal_features[feature]
            
            # 归一化处理
            total = visible_value + thermal_value
            if total > 0:
                visible_contribution = (visible_value / total) * weight
                thermal_contribution = (thermal_value / total) * weight
            else:
                visible_contribution = thermal_contribution = weight / 2
            
            visible_total += visible_contribution
            thermal_total += thermal_contribution
        
        # 确保权重总和为1
        if visible_total + thermal_total > 0:
            visible_weight = visible_total / (visible_total + thermal_total)
            thermal_weight = thermal_total / (visible_total + thermal_total)
        else:
            visible_weight = thermal_weight = 0.5
        
        return visible_weight, thermal_weight
    
    def judge_scene(self, features):
        """根据提取的特征判断当前场景是白天还是夜晚"""
        night_score = 0
        
        for feature, value in features.items():
            # 获取该特征的阈值
            threshold = self.scene_thresholds.get(feature, 0)
            weight = self.scene_weights.get(feature, 0.2)
            
            # 如果特征值低于阈值，认为更可能是夜晚
            if value < threshold:
                night_score += weight
        
        # 根据夜晚得分判断场景
        if night_score >= 0.5:
            return "夜晚"
        else:
            return "白天"
    
    def visualize_results(self, visible_features, thermal_features, visible_weight, thermal_weight):
        self.figure.clear()
        
        # 创建特征柱状图
        ax1 = self.figure.add_subplot(211)
        features = list(visible_features.keys())
        x = np.arange(len(features))
        width = 0.35
        
        visible_values = list(visible_features.values())
        thermal_values = list(thermal_features.values())
        
        # 归一化特征值以便于比较
        all_values = visible_values + thermal_values
        max_val = max(all_values) if all_values else 1
        
        visible_values_norm = [v / max_val for v in visible_values]
        thermal_values_norm = [v / max_val for v in thermal_values]
        
        rects1 = ax1.bar(x - width/2, visible_values_norm, width, label='可见光')
        rects2 = ax1.bar(x + width/2, thermal_values_norm, width, label='红外')
        
        ax1.set_ylabel('归一化特征值')
        ax1.set_title('特征提取结果')
        ax1.set_xticks(x)
        ax1.set_xticklabels(features, rotation=45)
        ax1.legend()
        
        def autolabel(rects):
            for rect in rects:
                height = rect.get_height()
                ax1.annotate('{:.2f}'.format(height),
                            xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3),  # 3 points vertical offset
                            textcoords="offset points",
                            ha='center', va='bottom')
        
        autolabel(rects1)
        autolabel(rects2)
        
        # 创建权重饼图
        ax2 = self.figure.add_subplot(212)
        labels = '可见光权重', '红外权重'
        sizes = [visible_weight, thermal_weight]
        explode = (0.1, 0)  # 突出显示可见光权重
        
        ax2.pie(sizes, explode=explode, labels=labels, autopct='%1.1f%%',
                shadow=True, startangle=90)
        ax2.axis('equal')  # 保证饼图是圆的
        ax2.set_title('模型分配权重')
        
        self.figure.tight_layout()
        self.canvas.draw()

if __name__ == "__main__":
    root = tk.Tk()
    app = MultimodalImageAnalyzer(root)
    root.mainloop()    