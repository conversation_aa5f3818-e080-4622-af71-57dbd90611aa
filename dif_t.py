import numpy as np
import cv2
import matplotlib.pyplot as plt
import pywt
from skimage.feature import hessian_matrix, hessian_matrix_eigvals
from skimage import measure
from scipy import ndimage
import os
#显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
def load_infrared_image(image_path):
    """加载红外图像并转换为灰度图"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图像文件不存在: {image_path}")
    
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"无法加载图像: {image_path}")
    
    # 转换为灰度图
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    else:
        gray = img
    
    # 归一化到[0, 1]范围
    normalized = gray.astype(np.float32) / 255.0
    return normalized

def wavelet_denoising(image, wavelet='db1', level=1):
    """小波去噪"""
    # 执行小波分解
    coeffs = pywt.wavedec2(image, wavelet, level=level)
    
    # 对细节系数进行阈值处理
    threshold = 0.1 * np.std(coeffs[-1][0])
    denoised_coeffs = list(coeffs)
    
    for i in range(1, len(coeffs)):
        denoised_coeffs[i] = tuple(pywt.threshold(c, threshold, mode='soft') for c in coeffs[i])
    
    # 重构图像
    denoised_image = pywt.waverec2(denoised_coeffs, wavelet)
    
    # 确保图像尺寸与原始图像一致
    if denoised_image.shape != image.shape:
        denoised_image = denoised_image[:image.shape[0], :image.shape[1]]
    
    return denoised_image

def anisotropic_diffusion(image, iterations=10, k=15, lambda_val=0.25):
    """各向异性扩散滤波"""
    # 创建图像副本进行处理
    diffused = image.copy()
    rows, cols = image.shape
    
    # 定义梯度算子
    dx = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=np.float32) / 8
    dy = dx.T
    dd1 = np.array([[1, 0, 0], [0, 0, 0], [0, 0, -1]], dtype=np.float32) / 2
    dd2 = np.array([[0, 0, 1], [0, 0, 0], [-1, 0, 0]], dtype=np.float32) / 2
    
    # 各向异性扩散迭代
    for i in range(iterations):
        # 计算梯度
        grad_x = cv2.filter2D(diffused, -1, dx)
        grad_y = cv2.filter2D(diffused, -1, dy)
        grad_d1 = cv2.filter2D(diffused, -1, dd1)
        grad_d2 = cv2.filter2D(diffused, -1, dd2)
        
        # 计算梯度幅值的平方
        grad_mag2 = grad_x**2 + grad_y**2 + grad_d1**2 + grad_d2**2
        
        # 计算扩散系数
        diff_coef_x = np.exp(-grad_x**2 / (k**2))
        diff_coef_y = np.exp(-grad_y**2 / (k**2))
        diff_coef_d1 = np.exp(-grad_d1**2 / (k**2))
        diff_coef_d2 = np.exp(-grad_d2**2 / (k**2))
        
        # 应用扩散
        diffused += lambda_val * (
            diff_coef_x * cv2.filter2D(grad_x, -1, -dx) +
            diff_coef_y * cv2.filter2D(grad_y, -1, -dy) +
            diff_coef_d1 * cv2.filter2D(grad_d1, -1, -dd1) +
            diff_coef_d2 * cv2.filter2D(grad_d2, -1, -dd2)
        )
    
    return diffused

def calculate_directional_gradients(image, directions=8):
    """计算不同方向的梯度"""
    rows, cols = image.shape
    gradient_magnitude = np.zeros((rows, cols))
    gradient_direction = np.zeros((rows, cols))
    
    # 计算水平和垂直梯度
    sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
    
    # 计算梯度幅值和方向
    gradient_magnitude = np.sqrt(sobelx**2 + sobely**2)
    gradient_direction = np.arctan2(sobely, sobelx) * 180 / np.pi
    
    # 将方向归一化到0-360度
    gradient_direction = (gradient_direction + 360) % 360
    
    # 计算各方向的梯度分布
    directional_gradients = np.zeros(directions)
    direction_bins = np.linspace(0, 360, directions + 1)
    
    for i in range(directions):
        mask = (gradient_direction >= direction_bins[i]) & (gradient_direction < direction_bins[i+1])
        directional_gradients[i] = np.mean(gradient_magnitude[mask]) if np.sum(mask) > 0 else 0
    
    return gradient_magnitude, gradient_direction, directional_gradients

def segment_target(image, threshold=0.5):
    """分割目标区域"""
    # 使用Otsu's方法自动确定阈值
    if threshold == 'otsu':
        _, binary = cv2.threshold((image * 255).astype(np.uint8), 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        binary = binary / 255
    else:
        binary = (image > threshold).astype(np.float32)
    
    # 形态学操作优化分割结果
    kernel = np.ones((1, 1), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 标记连通区域
    labels, num_labels = ndimage.label(binary)
    
    # 找到最大的连通区域作为目标
    if num_labels > 0:
        max_area = 0
        target_label = 0
        for i in range(1, num_labels + 1):
            area = np.sum(labels == i)
            if area > max_area:
                max_area = area
                target_label = i
        
        target_mask = (labels == target_label).astype(np.float32)
        return target_mask
    else:
        return binary

def analyze_geometric_differences(target_mask):
    """分析目标的几何特性"""
    # 计算轮廓
    contours, _ = cv2.findContours((target_mask * 255).astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if len(contours) == 0:
        return {
            'area': 0,
            'perimeter': 0,
            'circularity': 0,
            'eccentricity': 0,
            'orientation': 0,
            'major_axis_length': 0,
            'minor_axis_length': 0
        }
    
    # 计算几何特性
    cnt = contours[0]
    area = cv2.contourArea(cnt)
    perimeter = cv2.arcLength(cnt, True)
    
    # 计算圆形度
    circularity = 4 * np.pi * area / (perimeter ** 2) if perimeter > 0 else 0
    
    # 计算椭圆拟合参数
    if len(cnt) >= 5:  # 需要至少5个点来拟合椭圆
        ellipse = cv2.fitEllipse(cnt)
        (x, y), (major_axis, minor_axis), angle = ellipse
        eccentricity = np.sqrt(1 - (minor_axis / major_axis) ** 2) if major_axis > 0 else 0
        orientation = angle
    else:
        eccentricity = 0
        orientation = 0
        major_axis = 0
        minor_axis = 0
    
    return {
        'area': area,
        'perimeter': perimeter,
        'circularity': circularity,
        'eccentricity': eccentricity,
        'orientation': orientation,
        'major_axis_length': major_axis,
        'minor_axis_length': minor_axis
    }

def analyze_anisotropy(image, target_mask, directions=8):
    """分析目标区域的各向异性"""
    # 确保目标掩码与图像尺寸一致
    if target_mask.shape != image.shape:
        target_mask = cv2.resize(target_mask, (image.shape[1], image.shape[0]))
    
    # 计算整幅图像的梯度
    gradient_magnitude, gradient_direction, _ = calculate_directional_gradients(image, directions)
    
    # 分离目标和背景区域
    target_pixels = gradient_magnitude[target_mask > 0.5]
    background_pixels = gradient_magnitude[target_mask <= 0.5]
    
    # 计算目标和背景的平均梯度
    target_gradient_mean = np.mean(target_pixels) if len(target_pixels) > 0 else 0
    background_gradient_mean = np.mean(background_pixels) if len(background_pixels) > 0 else 0
    
    # 计算梯度变化率
    gradient_change_rate = target_gradient_mean / background_gradient_mean if background_gradient_mean > 0 else float('inf')
    
    # 计算目标区域的各向异性
    direction_bins = np.linspace(0, 360, directions + 1)
    directional_anisotropy = np.zeros(directions)
    
    for i in range(directions):
        # 选择特定方向的像素
        angle_mask = (gradient_direction >= direction_bins[i]) & (gradient_direction < direction_bins[i+1])
        target_directional_pixels = gradient_magnitude[(target_mask > 0.5) & angle_mask]
        background_directional_pixels = gradient_magnitude[(target_mask <= 0.5) & angle_mask]
        
        # 计算该方向上目标和背景的平均梯度
        if len(target_directional_pixels) > 0 and len(background_directional_pixels) > 0:
            target_dir_mean = np.mean(target_directional_pixels)
            background_dir_mean = np.mean(background_directional_pixels)
            directional_anisotropy[i] = target_dir_mean / background_dir_mean if background_dir_mean > 0 else float('inf')
    
    return {
        'gradient_change_rate': gradient_change_rate,
        'directional_anisotropy': directional_anisotropy,
        'target_gradient_mean': target_gradient_mean,
        'background_gradient_mean': background_gradient_mean
    }

def visualize_results(original, denoised, filtered, target_mask, anisotropy_results, geometric_results):
    """可视化分析结果"""
    directions = len(anisotropy_results['directional_anisotropy'])
    
    plt.figure(figsize=(15, 10))
    
    # 显示原始图像
    plt.subplot(2, 3, 1)
    plt.imshow(original, cmap='gray')
    plt.title('原始红外图像')
    plt.axis('off')
    
    # 显示小波去噪后的图像
    plt.subplot(2, 3, 2)
    plt.imshow(denoised, cmap='gray')
    plt.title('小波去噪后')
    plt.axis('off')
    
    # 显示各向异性滤波后的图像
    plt.subplot(2, 3, 3)
    plt.imshow(filtered, cmap='gray')
    plt.title('各向异性滤波后')
    plt.axis('off')
    
    # 显示目标分割结果
    plt.subplot(2, 3, 4)
    plt.imshow(original, cmap='gray')
    plt.imshow(target_mask, alpha=0.5, cmap='jet')
    plt.title('目标高热区域')
    plt.axis('off')
    
    # 显示梯度变化率和各向异性分析结果
    plt.subplot(2, 3, 5)
    angles = np.linspace(0, 2 * np.pi, directions, endpoint=False)
    anisotropy = anisotropy_results['directional_anisotropy']
    # 确保各向异性值在合理范围内以便可视化
    anisotropy = np.clip(anisotropy, 0, 5)
    
    # 闭合循环
    angles = np.append(angles, angles[0])
    anisotropy = np.append(anisotropy, anisotropy[0])
    
    plt.polar(angles, anisotropy, 'bo-', linewidth=2)
    plt.fill(angles, anisotropy, alpha=0.25)
    plt.title('方向各向异性分析')
    plt.grid(True)
    
    # 显示几何特性分析结果
    plt.subplot(2, 3, 6)
    plt.axis('off')
    plt.title('几何特性分析')
    
    props = [
        f"面积: {geometric_results['area']:.2f} 像素",
        f"周长: {geometric_results['perimeter']:.2f} 像素",
        f"圆形度: {geometric_results['circularity']:.4f}",
        f"离心率: {geometric_results['eccentricity']:.4f}",
        f"方向: {geometric_results['orientation']:.2f} 度",
        f"长轴长度: {geometric_results['major_axis_length']:.2f} 像素",
        f"短轴长度: {geometric_results['minor_axis_length']:.2f} 像素",
        f"梯度变化率: {anisotropy_results['gradient_change_rate']:.4f}"
    ]
    
    for i, prop in enumerate(props):
        plt.text(0.05, 0.9 - i * 0.1, prop, fontsize=10)
    
    plt.tight_layout()
    plt.show()

def main(image_path, wavelet_level=1, diffusion_iterations=10, threshold=0.5, directions=8):
    """主函数：执行完整的红外图像各向异性分析流程"""
    try:
        # 加载图像
        print("加载图像...")
        original = load_infrared_image(image_path)
        
        # 小波去噪
        print("执行小波去噪...")
        denoised = wavelet_denoising(original, level=wavelet_level)
        
        # 各向异性滤波
        print("执行各向异性滤波...")
        filtered = anisotropic_diffusion(denoised, iterations=diffusion_iterations)
        
        # 目标分割
        print("分割目标区域...")
        target_mask = segment_target(filtered, threshold)
        
        # 几何特性分析
        print("分析几何特性...")
        geometric_results = analyze_geometric_differences(target_mask)
        
        # 各向异性分析
        print("分析各向异性...")
        anisotropy_results = analyze_anisotropy(filtered, target_mask, directions)
        
        # 可视化结果
        print("可视化分析结果...")
        visualize_results(original, denoised, filtered, target_mask, anisotropy_results, geometric_results)
        
        # 输出结果
        print("\n分析结果:")
        print("=" * 50)
        print("几何特性:")
        for key, value in geometric_results.items():
            print(f"{key}: {value:.4f}")
        
        print("\n各向异性特性:")
        for key, value in anisotropy_results.items():
            if key == 'directional_anisotropy':
                print(f"{key}:")
                for i, dir_value in enumerate(value):
                    angle = i * (360 / directions)
                    print(f"  方向 {angle:3.0f}°: {dir_value:.4f}")
            else:
                print(f"{key}: {value:.4f}")
        print("=" * 50)
        
        return {
            'geometric_results': geometric_results,
            'anisotropy_results': anisotropy_results
        }
        
    except Exception as e:
        print(f"分析过程中发生错误: {e}")
        return None

if __name__ == "__main__":
    # 使用示例
    image_path = "infrared/000084.jpg"  # 替换为你的红外图像路径
    
    # 运行分析
    results = main(
        image_path=image_path,
        wavelet_level=1,
        diffusion_iterations=10,
        threshold=0.5,  # 可以设置为'otsu'使用Otsu自动阈值
        directions=8    # 分析的方向数量
    )    