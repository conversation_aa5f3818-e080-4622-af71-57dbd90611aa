import networkx as nx
import torch
import torch.nn as nn
from torch_geometric.nn import GCNConv

class KnowledgeGraph:
    def __init__(self, config):
        self.config = config
        self.device = config['device']
        self.target_classes = config['target_classes']
        
        # 初始化知识图谱
        self.graph = self._build_knowledge_graph()
        
        # 初始化GNN模型用于知识推理
        self.gnn_model = self._init_gnn_model().to(self.device)
    
    def _build_knowledge_graph(self):
        """构建目标识别知识图谱"""
        G = nx.DiGraph()
        
        # 添加实体节点
        # 场景节点
        for scene in self.config['scene_types']:
            G.add_node(scene, type='scene', description=f'Scene type: {scene}')
        
        # 目标类别节点
        for target in self.target_classes:
            G.add_node(target, type='target', description=f'Target class: {target}')
        
        # 添加关系边
        # 场景-目标关系（先验概率）
        scene_target_relations = {
            'sunny': {'carrier': 0.7, 'destroyer': 0.6, 'frigate': 0.5, 'submarine': 0.3, 'speedboat': 0.4},
            'rainy': {'carrier': 0.6, 'destroyer': 0.7, 'frigate': 0.6, 'submarine': 0.4, 'speedboat': 0.3},
            'night': {'carrier': 0.5, 'destroyer': 0.5, 'frigate': 0.4, 'submarine': 0.6, 'speedboat': 0.3},
            'foggy': {'carrier': 0.4, 'destroyer': 0.5, 'frigate': 0.5, 'submarine': 0.7, 'speedboat': 0.2}
        }
        
        for scene, targets in scene_target_relations.items():
            for target, prob in targets.items():
                G.add_edge(scene, target, relation='场景关联', weight=prob)
        
        # 目标-目标关系（相似度）
        target_similarities = {
            'carrier': {'destroyer': 0.3, 'frigate': 0.2, 'submarine': 0.1, 'speedboat': 0.1},
            'destroyer': {'carrier': 0.3, 'frigate': 0.6, 'submarine': 0.1, 'speedboat': 0.1},
            'frigate': {'carrier': 0.2, 'destroyer': 0.6, 'submarine': 0.1, 'speedboat': 0.1},
            'submarine': {'carrier': 0.1, 'destroyer': 0.1, 'frigate': 0.1, 'speedboat': 0.1},
            'speedboat': {'carrier': 0.1, 'destroyer': 0.1, 'frigate': 0.1, 'submarine': 0.1}
        }
        
        for target, others in target_similarities.items():
            for other, sim in others.items():
                if sim > 0:
                    G.add_edge(target, other, relation='相似度', weight=sim)
        
        return G
    
    def _init_gnn_model(self):
        """初始化图神经网络模型"""
        class GNN(nn.Module):
            def __init__(self, input_dim, hidden_dim, output_dim):
                super(GNN, self).__init__()
                self.conv1 = GCNConv(input_dim, hidden_dim)
                self.conv2 = GCNConv(hidden_dim, output_dim)
            
            def forward(self, x, edge_index):
                x = self.conv1(x, edge_index)
                x = F.relu(x)
                x = F.dropout(x, training=self.training)
                x = self.conv2(x, edge_index)
                return F.log_softmax(x, dim=1)
        
        # 假设每个节点用128维向量表示
        input_dim = 128
        hidden_dim = 64
        output_dim = len(self.target_classes)
        
        return GNN(input_dim, hidden_dim, output_dim)
    
    def get_constraints(self, scene_type):
        """获取给定场景下的知识约束"""
        # 从知识图谱中提取场景相关的约束
        constraints = {}
        
        # 获取场景对目标类别的先验概率
        for target in self.target_classes:
            if self.graph.has_edge(scene_type, target):
                constraints[target] = self.graph[scene_type][target]['weight']
            else:
                constraints[target] = 0.1  # 默认低概率
        
        return constraints
    
    def apply_knowledge(self, feature, scene_type):
        """应用知识图谱增强特征表示"""
        # 1. 获取场景约束
        scene_constraints = self.get_constraints(scene_type)
        
        # 2. 构建节点特征矩阵
        node_features = self._build_node_features(feature, scene_constraints)
        
        # 3. 转换为PyTorch Geometric格式
        edge_index, edge_weight = self._convert_graph_to_pyg(self.graph)
        
        # 4. 通过GNN模型进行知识推理
        with torch.no_grad():
            node_features = torch.FloatTensor(node_features).to(self.device)
            edge_index = torch.LongTensor(edge_index).to(self.device)
            edge_weight = torch.FloatTensor(edge_weight).to(self.device)
            
            output = self.gnn_model(node_features, edge_index)
        
        # 5. 提取目标类别预测
        target_preds = output[-len(self.target_classes):].cpu().numpy()
        
        return target_preds
    
    def _build_node_features(self, feature, scene_constraints):
        """构建知识图谱节点特征"""
        # 为简化示例，这里使用随机特征
        # 实际应用中应使用更复杂的特征工程
        node_features = np.random.rand(len(self.graph.nodes()), 128)
        
        # 将场景约束融入特征
        target_nodes = list(self.target_classes)
        for i, target in enumerate(target_nodes):
            constraint_weight = scene_constraints.get(target, 0.1)
            node_features[-len(target_nodes) + i] *= constraint_weight
        
        return node_features
    
    def _convert_graph_to_pyg(self, G):
        """将NetworkX图转换为PyTorch Geometric格式"""
        # 获取节点到索引的映射
        node_to_idx = {node: i for i, node in enumerate(G.nodes())}
        
        # 构建边索引和边权重
        edge_index = []
        edge_weight = []
        
        for u, v, data in G.edges(data=True):
            edge_index.append([node_to_idx[u], node_to_idx[v]])
            edge_weight.append(data['weight'])
        
        # 转换为PyTorch Geometric所需的格式
        edge_index = np.array(edge_index).T
        edge_weight = np.array(edge_weight)
        
        return edge_index, edge_weight    