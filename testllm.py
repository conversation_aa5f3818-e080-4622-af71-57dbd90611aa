import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 设置随机种子
torch.manual_seed(42)

class SelfAttention(nn.Module):
    def __init__(self, embed_dim):
        super(SelfAttention, self).__init__()
        self.embed_dim = embed_dim
        self.query = nn.Linear(embed_dim, embed_dim)
        self.key = nn.Linear(embed_dim, embed_dim)
        self.value = nn.Linear(embed_dim, embed_dim)
        self.scale = torch.sqrt(torch.tensor(embed_dim, dtype=torch.float32))
        
    def forward(self, x):
        # x: [batch_size, seq_len, embed_dim]
        Q = self.query(x)  # [batch_size, seq_len, embed_dim]
        K = self.key(x)    # [batch_size, seq_len, embed_dim]
        V = self.value(x)  # [batch_size, seq_len, embed_dim]
        
        # 计算注意力分数
        attn_scores = torch.matmul(Q, <PERSON>.transpose(-2, -1)) / self.scale  # [batch_size, seq_len, seq_len]
        attn_weights = torch.softmax(attn_scores, dim=-1)  # [batch_size, seq_len, seq_len]
        
        # 应用注意力权重
        output = torch.matmul(attn_weights, V)  # [batch_size, seq_len, embed_dim]
        return output, attn_weights

# 生成示例数据
batch_size = 1
seq_len = 5
embed_dim = 4
x = torch.randn(batch_size, seq_len, embed_dim)

# 初始化模型
attention = SelfAttention(embed_dim)

# 前向传播
output, attn_weights = attention(x)

# 可视化
plt.figure(figsize=(10, 5))

# 绘制注意力权重热图
plt.subplot(1, 2, 1)
sns.heatmap(attn_weights[0].detach().numpy(), annot=True, cmap='viridis')
plt.title('Attention Weights')

# 绘制输入输出对比
plt.subplot(1, 2, 2)
input_output = torch.cat([x, output], dim=2).detach().numpy()
sns.heatmap(input_output[0], annot=True, cmap='coolwarm')
plt.title('Input vs Output')
plt.tight_layout()
plt.show()

print("输入形状:", x.shape)
print("输出形状:", output.shape)
print("注意力权重形状:", attn_weights.shape)
