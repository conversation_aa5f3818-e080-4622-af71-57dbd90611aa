#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多模态识别系统测试脚本
"""

import sys
import os
import numpy as np
import cv2

def create_test_images():
    """创建测试图像"""
    test_dir = "test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建可见光测试图像（模拟白天场景）
    visible_img = np.random.randint(100, 255, (480, 640, 3), dtype=np.uint8)
    # 添加一些结构
    cv2.rectangle(visible_img, (100, 100), (200, 200), (255, 255, 255), -1)
    cv2.rectangle(visible_img, (300, 200), (400, 300), (0, 255, 0), -1)
    cv2.imwrite(os.path.join(test_dir, "visible_day.jpg"), visible_img)
    
    # 创建红外测试图像（模拟夜晚场景）
    thermal_img = np.random.randint(50, 150, (480, 640, 3), dtype=np.uint8)
    # 添加热点
    cv2.circle(thermal_img, (320, 240), 50, (200, 200, 200), -1)
    cv2.circle(thermal_img, (150, 150), 30, (180, 180, 180), -1)
    cv2.imwrite(os.path.join(test_dir, "thermal_night.jpg"), thermal_img)
    
    # 创建SAR测试图像
    sar_img = np.random.randint(0, 255, (480, 640), dtype=np.uint8)
    # 添加一些SAR特征
    for i in range(10):
        x, y = np.random.randint(0, 640), np.random.randint(0, 480)
        cv2.circle(sar_img, (x, y), 20, 255, 2)
    cv2.imwrite(os.path.join(test_dir, "sar_image.jpg"), sar_img)
    
    print(f"测试图像已创建在 {test_dir} 目录中")
    return test_dir

def test_imports():
    """测试导入"""
    try:
        from multimodal_recognition_system import (
            DataTypeDetector,
            MultiDomainFeatureExtractor,
            SceneRecognizer,
            ProcessDataManager,
            DetectionSystemManager,
            YOLODetectionProcessor,
            GradientFeatureExtractor
        )
        print("✓ 所有核心模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_data_type_detection():
    """测试数据类型检测"""
    try:
        from multimodal_recognition_system import DataTypeDetector
        
        test_dir = create_test_images()
        
        # 测试可见光图像检测
        visible_path = os.path.join(test_dir, "visible_day.jpg")
        result = DataTypeDetector.detect_data_type(visible_path)
        print(f"✓ 可见光图像检测: {result}")
        
        # 测试红外图像检测
        thermal_path = os.path.join(test_dir, "thermal_night.jpg")
        result = DataTypeDetector.detect_data_type(thermal_path)
        print(f"✓ 红外图像检测: {result}")
        
        # 测试SAR图像检测
        sar_path = os.path.join(test_dir, "sar_image.jpg")
        result = DataTypeDetector.detect_data_type(sar_path)
        print(f"✓ SAR图像检测: {result}")
        
        return True
    except Exception as e:
        print(f"✗ 数据类型检测测试失败: {e}")
        return False

def test_feature_extraction():
    """测试特征提取"""
    try:
        from multimodal_recognition_system import MultiDomainFeatureExtractor
        
        # 创建测试图像
        test_img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # 测试空域特征提取
        spatial_features = MultiDomainFeatureExtractor.extract_spatial_features(test_img)
        print(f"✓ 空域特征提取: {len(spatial_features)} 个特征")
        
        # 测试频域特征提取
        frequency_features = MultiDomainFeatureExtractor.extract_frequency_features(test_img)
        print(f"✓ 频域特征提取: {len(frequency_features)} 个特征")
        
        # 测试小波域特征提取
        wavelet_features = MultiDomainFeatureExtractor.extract_wavelet_features(test_img)
        print(f"✓ 小波域特征提取: {len(wavelet_features)} 个特征")
        
        return True
    except Exception as e:
        print(f"✗ 特征提取测试失败: {e}")
        return False

def test_scene_recognition():
    """测试场景识别"""
    try:
        from multimodal_recognition_system import SceneRecognizer, MultiDomainFeatureExtractor
        
        # 创建模拟的白天和夜晚图像特征
        day_img = np.random.randint(150, 255, (100, 100, 3), dtype=np.uint8)
        night_img = np.random.randint(0, 80, (100, 100, 3), dtype=np.uint8)
        
        day_features = MultiDomainFeatureExtractor.extract_spatial_features(day_img)
        night_features = MultiDomainFeatureExtractor.extract_spatial_features(night_img)
        
        # 测试场景识别
        scene_result = SceneRecognizer.recognize_scene(day_features, night_features)
        print(f"✓ 场景识别: {scene_result['scene']} (置信度: {scene_result['confidence']:.2f})")
        
        return True
    except Exception as e:
        print(f"✗ 场景识别测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("多模态识别系统测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("数据类型检测测试", test_data_type_detection),
        ("特征提取测试", test_feature_extraction),
        ("场景识别测试", test_scene_recognition),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行。")
        print("运行 'python run_multimodal_system.py' 启动完整系统。")
    else:
        print("⚠️  部分测试失败，请检查依赖包安装。")
        print("运行 'pip install -r requirements.txt' 安装依赖。")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
