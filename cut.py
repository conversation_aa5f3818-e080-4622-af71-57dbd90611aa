import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
from datetime import datetime
#显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文
def load_and_preprocess_image(image_path, target_size=None):
    """
    加载图像并进行预处理
    
    参数:
    image_path (str): 图像文件路径
    target_size (tuple): 目标尺寸，默认为None
    
    返回:
    np.ndarray: 预处理后的图像
    """
    try:
        # 读取图像
        image = cv2.imread(str(image_path))
        if image is None:
            raise FileNotFoundError(f"无法读取图像: {image_path}")
            
        # 转换为灰度图（用于特征匹配）
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 如果提供了目标尺寸，则调整图像大小
        if target_size:
            gray = cv2.resize(gray, target_size)
            
        return gray, image
    except Exception as e:
        print(f"加载图像时出错: {e}")
        return None, None

def find_matching_region(visible_img, ir_img, feature_method='ORB'):
    """
    找出两个图像中匹配的区域
    
    参数:
    visible_img (np.ndarray): 可见光图像
    ir_img (np.ndarray): 红外图像
    feature_method (str): 特征检测方法，支持'ORB'或'SIFT'
    
    返回:
    tuple: 可见光图像和红外图像的匹配区域坐标
    """
    try:
        # 初始化特征检测器
        if feature_method == 'ORB':
            detector = cv2.ORB_create()
        elif feature_method == 'SIFT':
            detector = cv2.SIFT_create()
        else:
            raise ValueError("不支持的特征检测方法")
        
        # 检测关键点和描述符
        kp1, des1 = detector.detectAndCompute(visible_img, None)
        kp2, des2 = detector.detectAndCompute(ir_img, None)
        
        # 特征匹配
        if feature_method == 'ORB':
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
        else:  # SIFT
            bf = cv2.BFMatcher()
            
        matches = bf.match(des1, des2)
        
        # 按距离排序
        matches = sorted(matches, key=lambda x: x.distance)
        
        # 获取匹配点
        src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
        dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
        
        # 计算单应性矩阵
        H, _ = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
        
        # 获取可见光图像的角点
        h, w = visible_img.shape
        pts = np.float32([[0, 0], [0, h-1], [w-1, h-1], [w-1, 0]]).reshape(-1, 1, 2)
        
        # 将角点投影到红外图像上
        dst = cv2.perspectiveTransform(pts, H)
        
        # 计算包围盒
        x_min = int(max(0, np.min(dst[:, :, 0])))
        y_min = int(max(0, np.min(dst[:, :, 1])))
        x_max = int(min(ir_img.shape[1], np.max(dst[:, :, 0])))
        y_max = int(min(ir_img.shape[0], np.max(dst[:, :, 1])))
        
        # 计算可见光图像对应的区域
        H_inv = np.linalg.inv(H)
        ir_corners = np.float32([[x_min, y_min], [x_min, y_max], [x_max, y_max], [x_max, y_min]]).reshape(-1, 1, 2)
        visible_corners = cv2.perspectiveTransform(ir_corners, H_inv)
        
        v_x_min = int(max(0, np.min(visible_corners[:, :, 0])))
        v_y_min = int(max(0, np.min(visible_corners[:, :, 1])))
        v_x_max = int(min(visible_img.shape[1], np.max(visible_corners[:, :, 0])))
        v_y_max = int(min(visible_img.shape[0], np.max(visible_corners[:, :, 1])))
        
        return (v_x_min, v_y_min, v_x_max, v_y_max), (x_min, y_min, x_max, y_max)
    except Exception as e:
        print(f"查找匹配区域时出错: {e}")
        return None, None

def crop_and_resize_images(visible_img, ir_img, visible_roi, ir_roi, target_size=None):
    """
    裁剪并调整图像大小
    
    参数:
    visible_img (np.ndarray): 可见光图像
    ir_img (np.ndarray): 红外图像
    visible_roi (tuple): 可见光图像的感兴趣区域
    ir_roi (tuple): 红外图像的感兴趣区域
    target_size (tuple): 目标尺寸，默认为None
    
    返回:
    tuple: 裁剪并调整大小后的可见光图像和红外图像
    """
    try:
        # 裁剪图像
        v_x_min, v_y_min, v_x_max, v_y_max = visible_roi
        x_min, y_min, x_max, y_max = ir_roi
        
        cropped_visible = visible_img[v_y_min:v_y_max, v_x_min:v_x_max]
        cropped_ir = ir_img[y_min:y_max, x_min:x_max]
        
        # 如果没有指定目标尺寸，则使用裁剪后的可见光图像尺寸
        if not target_size:
            target_size = (cropped_visible.shape[1], cropped_visible.shape[0])
        
        # 调整图像大小
        resized_visible = cv2.resize(cropped_visible, target_size)
        resized_ir = cv2.resize(cropped_ir, target_size)
        
        return resized_visible, resized_ir
    except Exception as e:
        print(f"裁剪和调整图像大小时出错: {e}")
        return None, None

def visualize_results(original_visible, original_ir, cropped_visible, cropped_ir):
    """
    可视化处理结果
    
    参数:
    original_visible (np.ndarray): 原始可见光图像
    original_ir (np.ndarray): 原始红外图像
    cropped_visible (np.ndarray): 裁剪后的可见光图像
    cropped_ir (np.ndarray): 裁剪后的红外图像
    """
    try:
        plt.figure(figsize=(12, 10))
        
        plt.subplot(221)
        plt.title('原始可见光图像')
        if len(original_visible.shape) == 3:
            plt.imshow(cv2.cvtColor(original_visible, cv2.COLOR_BGR2RGB))
        else:
            plt.imshow(original_visible, cmap='gray')
        plt.axis('off')
        
        plt.subplot(222)
        plt.title('原始红外图像')
        if len(original_ir.shape) == 3:
            plt.imshow(cv2.cvtColor(original_ir, cv2.COLOR_BGR2RGB))
        else:
            plt.imshow(original_ir, cmap='gray')
        plt.axis('off')
        
        plt.subplot(223)
        plt.title('裁剪并调整大小后的可见光图像')
        if len(cropped_visible.shape) == 3:
            plt.imshow(cv2.cvtColor(cropped_visible, cv2.COLOR_BGR2RGB))
        else:
            plt.imshow(cropped_visible, cmap='gray')
        plt.axis('off')
        
        plt.subplot(224)
        plt.title('裁剪并调整大小后的红外图像')
        if len(cropped_ir.shape) == 3:
            plt.imshow(cv2.cvtColor(cropped_ir, cv2.COLOR_BGR2RGB))
        else:
            plt.imshow(cropped_ir, cmap='gray')
        plt.axis('off')
        
        plt.tight_layout()
        plt.show()
    except Exception as e:
        print(f"可视化结果时出错: {e}")

def save_processed_images(visible_img, ir_img, output_dir=None, prefix='processed'):
    """
    保存处理后的图像
    
    参数:
    visible_img (np.ndarray): 处理后的可见光图像
    ir_img (np.ndarray): 处理后的红外图像
    output_dir (str): 输出目录，默认为None
    prefix (str): 文件名前缀，默认为'processed'
    """
    try:
        if output_dir is None:
            output_dir = Path.cwd() / 'output'
        else:
            output_dir = Path(output_dir)
            
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 添加时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存图像
        visible_path = output_dir / f"{prefix}_visible_{timestamp}.png"
        ir_path = output_dir / f"{prefix}_ir_{timestamp}.png"
        
        # 确保图像格式正确
        if len(visible_img.shape) == 2:
            visible_img = cv2.cvtColor(visible_img, cv2.COLOR_GRAY2BGR)
        if len(ir_img.shape) == 2:
            ir_img = cv2.cvtColor(ir_img, cv2.COLOR_GRAY2BGR)
            
        cv2.imwrite(str(visible_path), visible_img)
        cv2.imwrite(str(ir_path), ir_img)
        
        print(f"已保存处理后的图像到: {output_dir}")
        return visible_path, ir_path
    except Exception as e:
        print(f"保存图像时出错: {e}")
        return None, None

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='处理可见光和红外图像，裁剪匹配区域并调整为相同大小')
    parser.add_argument('--visible', required=True, help='可见光图像路径')
    parser.add_argument('--ir', required=True, help='红外图像路径')
    parser.add_argument('--output', default='results/', help='输出目录路径')
    parser.add_argument('--target-size', type=int, nargs=2, metavar=('WIDTH', 'HEIGHT'),
                        help='目标图像大小 (宽度, 高度)')
    parser.add_argument('--feature-method', choices=['ORB', 'SIFT'], default='ORB', 
                        help='特征检测方法 (ORB 或 SIFT)')
    parser.add_argument('--no-visualize', action='store_true', help='不显示结果可视化')
    
    args = parser.parse_args()
    
    # 加载图像
    print("正在加载图像...")
    visible_gray, visible_color = load_and_preprocess_image(args.visible)
    ir_gray, ir_color = load_and_preprocess_image(args.ir)
    
    if visible_gray is None or ir_gray is None:
        print("无法加载图像，程序退出")
        return
    
    # 查找匹配区域
    print("正在查找匹配区域...")
    visible_roi, ir_roi = find_matching_region(visible_gray, ir_gray, args.feature_method)
    
    if visible_roi is None or ir_roi is None:
        print("无法找到匹配区域，程序退出")
        return
    
    # 裁剪并调整图像大小
    print("正在裁剪并调整图像大小...")
    target_size = tuple(args.target_size) if args.target_size else None
    processed_visible, processed_ir = crop_and_resize_images(
        visible_color if visible_color is not None else visible_gray,
        ir_color if ir_color is not None else ir_gray,
        visible_roi, ir_roi, target_size
    )
    
    if processed_visible is None or processed_ir is None:
        print("无法处理图像，程序退出")
        return
    
    # 保存处理后的图像
    save_processed_images(processed_visible, processed_ir, args.output)
    
    # 可视化结果
    if not args.no_visualize:
        visualize_results(
            visible_color if visible_color is not None else visible_gray,
            ir_color if ir_color is not None else ir_gray,
            processed_visible, processed_ir
        )

if __name__ == "__main__":
    main()    