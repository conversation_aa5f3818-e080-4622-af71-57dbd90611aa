from rdflib import Graph, Namespace, Literal, RDF, URIRef
from rdflib.namespace import XSD
import uuid

# ──────────────── 定义命名空间 ────────────────
EX = Namespace("http://example.org/")

# ──────────────── 构造 RDF 图 ────────────────
g = Graph()
g.bind("ex", EX)

def add_part(graph, part_name, cx):
    """添加部件及其x中心坐标"""
    part_uri = URIRef(f"http://example.org/part/{part_name}_{uuid.uuid4().hex[:6]}")
    graph.add((part_uri, RDF.type, EX.Part))
    graph.add((part_uri, EX.label, Literal(part_name)))
    graph.add((part_uri, EX.cx, Literal(cx, datatype=XSD.float)))
    return part_uri

def add_left_of(graph, part1, part2):
    graph.add((part1, EX.leftOf, part2))

def add_between(graph, partC, partL, partR):
    graph.add((partC, EX.between, partL))
    graph.add((partC, EX.between, partR))

def build_scene_graph(detections):
    """构造 RDF 图，并注入空间关系"""
    g = Graph()
    g.bind("ex", EX)

    # 添加所有部件
    part_map = {}
    for d in detections:
        label, bbox = d["label"], d["bbox"]
        cx = (bbox[0] + bbox[2]) / 2.0
        part_map[label] = add_part(g, label, cx)

    # 添加空间关系（模拟推理）
    if "A" in part_map and "B" in part_map:
        if get_cx(part_map["A"], g) < get_cx(part_map["B"], g):
            add_left_of(g, part_map["A"], part_map["B"])

    if all(p in part_map for p in ["B", "C", "D"]):
        cxB = get_cx(part_map["B"], g)
        cxC = get_cx(part_map["C"], g)
        cxD = get_cx(part_map["D"], g)
        if min(cxB, cxD) < cxC < max(cxB, cxD):
            add_between(g, part_map["C"], part_map["B"], part_map["D"])

    return g

def get_cx(uri, graph):
    """获取某个部件中心坐标"""
    return float(next(graph.objects(uri, EX.cx)))

# ──────────────── SPARQL 规则推理查询 ────────────────
def query_individual(g: Graph) -> str:
    # 规则1：A leftOf B
    q1 = """
    PREFIX ex: <http://example.org/>
    SELECT ?a ?b
    WHERE {
        ?a ex:label "A" .
        ?b ex:label "B" .
        ?a ex:leftOf ?b .
    }
    """
    res1 = list(g.query(q1))
    if res1:
        return "Individual-1"

    # 规则2：C between B and D
    q2 = """
    PREFIX ex: <http://example.org/>
    SELECT ?c
    WHERE {
        ?c ex:label "C" .
        ?b ex:label "B" .
        ?d ex:label "D" .
        ?c ex:between ?b .
        ?c ex:between ?d .
    }
    """
    res2 = list(g.query(q2))
    if res2:
        return "Individual-2"

    return "Unknown"

# ──────────────── 示例调用 ────────────────
detections_example = [
    {"label": "A", "bbox": (50, 50, 100, 100)},
    {"label": "B", "bbox": (150, 60, 200, 120)},
    # {"label": "C", "bbox": (120, 40, 170, 90)},  # 取消注释可测试 Individual-2
    # {"label": "D", "bbox": (200, 50, 250, 100)},
]

rdf_graph = build_scene_graph(detections_example)
result = query_individual(rdf_graph)
print("推理结果:", result)
