import torch
import cv2
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtCore import Qt, QTimer
from models import SaliencyDetector, AnisotropyAnalyzer
from utils import preprocess_image, postprocess_saliency, visualize_anisotropy

class ThermalVisionAnalysisSystem(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_models()
        self.init_video_capture()
        self.init_timer()
        
    def init_ui(self):
        self.setWindowTitle("红外-可见光图像分析系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建标签用于显示图像
        self.ir_label = QLabel("红外图像")
        self.visible_label = QLabel("可见光图像")
        self.saliency_label = QLabel("显著性检测结果")
        self.anisotropy_label = QLabel("各向异性分析结果")
        
        # 设置布局
        layout = QVBoxLayout()
        layout.addWidget(self.ir_label)
        layout.addWidget(self.visible_label)
        layout.addWidget(self.saliency_label)
        layout.addWidget(self.anisotropy_label)
        
        central_widget = QWidget()
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
    
    def init_models(self):
        # 加载显著性检测模型
        self.saliency_detector = SaliencyDetector()
        self.saliency_detector.load_state_dict(torch.load('models/saliency_model.pth'))
        self.saliency_detector.eval()
        
        # 加载各向异性分析模型
        self.anisotropy_analyzer = AnisotropyAnalyzer()
        self.anisotropy_analyzer.load_state_dict(torch.load('models/anisotropy_model.pth'))
        self.anisotropy_analyzer.eval()
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.saliency_detector.to(self.device)
        self.anisotropy_analyzer.to(self.device)
    
    def init_video_capture(self):
        # 初始化摄像头或视频文件
        self.ir_cap = cv2.VideoCapture('path/to/ir_video.mp4')
        self.visible_cap = cv2.VideoCapture('path/to/visible_video.mp4')
    
    def init_timer(self):
        # 设置定时器，用于定时更新图像
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_frame)
        self.timer.start(30)  # 30ms更新一次
    
    def update_frame(self):
        # 读取图像帧
        ret_ir, ir_frame = self.ir_cap.read()
        ret_visible, visible_frame = self.visible_cap.read()
        
        if not ret_ir or not ret_visible:
            return
        
        # 预处理图像
        ir_tensor = preprocess_image(ir_frame, self.device)
        visible_tensor = preprocess_image(visible_frame, self.device)
        
        # 计算图像清晰度，决定是否使用可见光辅助
        visible_sharpness = self.calculate_sharpness(visible_frame)
        use_visible = visible_sharpness > 100  # 阈值可调整
        
        # 显著性检测
        with torch.no_grad():
            if use_visible:
                saliency_map = self.saliency_detector(ir_tensor, visible_tensor)
            else:
                saliency_map = self.saliency_detector(ir_tensor)
        
        # 后处理显著性图
        saliency_mask = postprocess_saliency(saliency_map)
        
        # 提取目标区域
        ir_gray = cv2.cvtColor(ir_frame, cv2.COLOR_BGR2GRAY)
        target_region = ir_gray * saliency_mask
        
        # 各向异性分析
        with torch.no_grad():
            anisotropy_result = self.anisotropy_analyzer(target_region)
        
        # 可视化结果
        saliency_vis = self.visualize_saliency(ir_frame, saliency_mask)
        anisotropy_vis = visualize_anisotropy(target_region, anisotropy_result)
        
        # 在界面上显示图像
        self.display_image(self.ir_label, ir_frame)
        self.display_image(self.visible_label, visible_frame)
        self.display_image(self.saliency_label, saliency_vis)
        self.display_image(self.anisotropy_label, anisotropy_vis)
    
    def calculate_sharpness(self, image):
        # 计算图像清晰度（拉普拉斯方差）
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return cv2.Laplacian(gray, cv2.CV_64F).var()
    
    def visualize_saliency(self, image, mask):
        # 将显著性掩码叠加到原图上
        mask = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
        return cv2.addWeighted(image, 0.7, mask, 0.3, 0)
    
    def display_image(self, label, image):
        # 将OpenCV图像转换为Qt图像并显示
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        label.setPixmap(QPixmap.fromImage(q_image).scaled(
            label.width(), label.height(), Qt.KeepAspectRatio))

if __name__ == "__main__":
    app = QApplication([])
    window = ThermalVisionAnalysisSystem()
    window.show()
    app.exec_()    