import torch
import torch.nn as nn
from torchvision import models
import numpy as np

class FeatureExtractor:
    def __init__(self, config):
        self.config = config
        self.device = config['device']
        self.feature_dim = config['feature_dim']
        
        # 初始化多模态特征提取器
        self.visible_model = self._init_visible_model()
        self.infrared_model = self._init_infrared_model()
        self.sar_model = self._init_sar_model()
        
        # 冻结预训练模型参数
        for param in self.visible_model.parameters():
            param.requires_grad = False
        
        for param in self.infrared_model.parameters():
            param.requires_grad = False
        
        for param in self.sar_model.parameters():
            param.requires_grad = False
    
    def _init_visible_model(self):
        """初始化可见光图像特征提取模型"""
        model = models.resnet50(pretrained=True)
        # 移除最后一层全连接层
        model = nn.Sequential(*list(model.children())[:-1])
        model = model.to(self.device)
        model.eval()
        return model
    
    def _init_infrared_model(self):
        """初始化红外图像特征提取模型"""
        # 由于红外图像是单通道，修改输入层
        model = models.resnet18(pretrained=True)
        model.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)
        # 加载预训练权重到除输入层外的其他层
        pretrained_dict = models.resnet18(pretrained=True).state_dict()
        model_dict = model.state_dict()
        pretrained_dict = {k: v for k, v in pretrained_dict.items() if k in model_dict and 'conv1' not in k}
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict)
        
        # 移除最后一层全连接层
        model = nn.Sequential(*list(model.children())[:-1])
        model = model.to(self.device)
        model.eval()
        return model
    
    def _init_sar_model(self):
        """初始化SAR图像特征提取模型"""
        # 针对SAR图像特点设计的模型
        class SARFeatureExtractor(nn.Module):
            def __init__(self):
                super(SARFeatureExtractor, self).__init__()
                self.conv1 = nn.Conv2d(3, 32, kernel_size=3, stride=1, padding=1)
                self.conv2 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)
                self.pool = nn.MaxPool2d(2, 2)
                self.resnet = models.resnet18(pretrained=True)
                # 替换第一个卷积层以适应SAR图像
                self.resnet.conv1 = nn.Conv2d(64, 64, kernel_size=7, stride=2, padding=3, bias=False)
                # 移除最后一层
                self.resnet = nn.Sequential(*list(self.resnet.children())[:-1])
            
            def forward(self, x):
                x = F.relu(self.conv1(x))
                x = self.pool(F.relu(self.conv2(x)))
                x = self.resnet(x)
                return x
        
        model = SARFeatureExtractor().to(self.device)
        model.eval()
        return model
    
    def extract_features(self, processed_data):
        """从多源数据中提取特征"""
        features = {}
        
        with torch.no_grad():
            # 提取可见光特征
            visible_features = self.visible_model(processed_data['visible'])
            visible_features = visible_features.view(visible_features.size(0), -1)
            features['visible'] = visible_features.cpu().numpy()
            
            # 提取红外特征
            infrared_data = processed_data['infrared']
            # 如果是RGB格式，转换为单通道
            if infrared_data.shape[1] == 3:
                infrared_data = torch.mean(infrared_data, dim=1, keepdim=True)
            infrared_features = self.infrared_model(infrared_data)
            infrared_features = infrared_features.view(infrared_features.size(0), -1)
            features['infrared'] = infrared_features.cpu().numpy()
            
            # 提取SAR特征
            sar_features = self.sar_model(processed_data['sar'])
            sar_features = sar_features.view(sar_features.size(0), -1)
            features['sar'] = sar_features.cpu().numpy()
        
        return features
    
    def evaluate_features(self, features):
        """评估特征质量"""
        metrics = {}
        
        for sensor_type, feature in features.items():
            # 计算特征的判别力（方差）
            discriminability = np.var(feature)
            
            # 计算特征的稳定性（与均值的平均距离）
            stability = np.mean(np.abs(feature - np.mean(feature)))
            
            # 计算特征的信息熵
            normalized_feature = (feature - feature.min()) / (feature.max() - feature.min() + 1e-6)
            normalized_feature = normalized_feature.flatten()
            normalized_feature = normalized_feature[normalized_feature > 0]  # 避免log(0)
            entropy = -np.sum(normalized_feature * np.log2(normalized_feature))
            
            metrics[sensor_type] = {
                'discriminability': discriminability,
                'stability': stability,
                'entropy': entropy,
                'weight': discriminability * stability * entropy  # 综合权重
            }
        
        return metrics    